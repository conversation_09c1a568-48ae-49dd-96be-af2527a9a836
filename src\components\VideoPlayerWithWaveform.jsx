/* eslint-disable no-unused-vars */
// VideoPlayerWithWaveform.js
import React, { useEffect, useState } from "react";
import VideoSetting from "./VideoSetting";
import VideoTimeline from "./VideoTimeline";
import VideoContainer from "../components/video-container/VideoContainer";
import ImportMediaModal from "./modal/ImportMediaModal";
import ExportMarkersModal from "./modal/ExportMarkersModal";
import ExportModal from "./modal/ExportModal";
import { useVideoPlayer } from "../hooks/useVideoPlayer";
import { useDragAndDrop } from "../hooks/useDragAndDrop";
import "../assets/css/VideoPlayerWithWaveform.css";
import Help from "./Help";
import { useAppContext } from "../hooks/useAppContext";

const VideoPlayerWithWaveform = ({
  initialVideoSrc = null,
  initialVideoFile = null,
}) => {
  const videoPlayerProps = useVideoPlayer(initialVideoSrc, initialVideoFile);
  const [videoPathProps, setVideoPathProps] = useState("");
  const [projectFilePath, setProjectFilePath] = useState(null);
  // New states for timeline features
  const [selectedSegment, setSelectedSegment] = useState(null);
  const [segments, setSegments] = useState([]);

  const { page } = useAppContext();

  const handleVideoFile = (file) => {
    videoPlayerProps.setVideoSource(file);
    setVideoPathProps(file);
    // Reset segments when new video is loaded
    setSegments([]);
    setSelectedSegment(null);
  };

  const dragDropProps = useDragAndDrop(
    handleVideoFile,
    !!videoPlayerProps.videoSrc
  );

  const handleSegmentUpdate = (updatedSegment) => {
    console.log("Updating segment from VideoSetting:", updatedSegment);

    // Update the segments array with the modified segment
    setSegments((currentSegments) =>
      currentSegments.map((seg) =>
        seg.id === updatedSegment.id ? updatedSegment : seg
      )
    );

    // Update the selected segment
    setSelectedSegment(updatedSegment);
  };

  // Handle segment selection from timeline
  const handleSegmentSelect = (segment) => {
    setSelectedSegment(segment);

    // If a segment is selected, you might want to jump to its start time
    if (segment && videoPlayerProps.handleSeek) {
      videoPlayerProps.handleSeek(segment.start);
    }
  };

  // Handle segments change from timeline
  const handleSegmentsChange = (newSegments) => {
    setSegments(newSegments);
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Only handle shortcuts when not typing in input fields
      if (e.target.tagName === "INPUT" || e.target.tagName === "TEXTAREA")
        return;

      switch (e.key.toLowerCase()) {
        case " ":
          e.preventDefault();
          videoPlayerProps.togglePlay?.();
          break;
        case "arrowleft":
          e.preventDefault();
          if (videoPlayerProps.handleSeek) {
            const newTime = Math.max(0, videoPlayerProps.currentTime - 5);
            videoPlayerProps.handleSeek(newTime);
          }
          break;
        case "arrowright":
          e.preventDefault();
          if (videoPlayerProps.handleSeek) {
            const newTime = Math.min(
              videoPlayerProps.duration,
              videoPlayerProps.currentTime + 5
            );
            videoPlayerProps.handleSeek(newTime);
          }
          break;
        case "delete":
          if (selectedSegment) {
            const newSegments = segments.filter(
              (seg) => seg !== selectedSegment
            );
            setSegments(newSegments);
            setSelectedSegment(null);
          }
          break;
        case "e":
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            exportSegments();
          }
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [videoPlayerProps, selectedSegment, segments]);

  useEffect(() => {
    const handleOpenProject = async (event, filePath) => {
      try {
        // Store the project file path - THIS IS THE KEY ADDITION
        setProjectFilePath(filePath);
        console.log("Project file path set to:", filePath);

        let projectData;
        let project;

        if (
          window.electronAPI &&
          typeof window.electronAPI.readFile === "function"
        ) {
          projectData = window.electronAPI.readFile(filePath, "utf8");
          project = JSON.parse(projectData);
        } else if (
          window.electronAPI &&
          typeof window.electronAPI.openProject === "function"
        ) {
          const result = await window.electronAPI.openProject();
          if (result.success) {
            project = result.projectData;
            // If we got project data but no filePath, try to get it from the result
            if (!filePath && result.filePath) {
              setProjectFilePath(result.filePath);
              console.log(
                "Project file path set from result:",
                result.filePath
              );
            }
          } else {
            throw new Error(result.error || "Failed to open project");
          }
        } else {
          throw new Error("electronAPI methods not available");
        }

        // Handle different project formats
        let videoSource = null;
        let videoFound = false;

        // Priority 1: Check for embedded video (new format)
        if (project.video?.embedded) {
          try {
            const binaryString = atob(project.video.embedded.data);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }

            const blob = new Blob([bytes], {
              type: project.video.embedded.mimeType,
            });
            const videoUrl = URL.createObjectURL(blob);

            videoSource = videoUrl;
            videoFound = true;
          } catch (error) {
            console.error("❌ Failed to restore embedded video:", error);
          }
        }
        // Priority 2: Check for video property (string filename) - THIS IS YOUR CASE
        else if (project.video && typeof project.video === "string") {
          let projectDir;
          if (filePath.includes("\\")) {
            projectDir = filePath.substring(0, filePath.lastIndexOf("\\"));
          } else {
            projectDir = filePath.substring(0, filePath.lastIndexOf("/"));
          }

          const videoPath = `${projectDir}/${project.video}`;
          console.log("Constructed video path from project:", videoPath);

          if (
            window.electronAPI &&
            typeof window.electronAPI.fileExists === "function"
          ) {
            const videoExists = window.electronAPI.fileExists(videoPath);
            if (videoExists) {
              videoSource = videoPath;
              videoFound = true;
              console.log("Video file found at:", videoPath);
            } else {
              console.warn("Video file not found at:", videoPath);
            }
          }
        }
        // Priority 3: Check for video file in project folder (nested video object)
        else if (project.video?.video) {
          let projectDir;
          if (filePath.includes("\\")) {
            projectDir = filePath.substring(0, filePath.lastIndexOf("\\"));
          } else {
            projectDir = filePath.substring(0, filePath.lastIndexOf("/"));
          }

          const videoPath = `${projectDir}/${project.video.video}`;

          if (
            window.electronAPI &&
            typeof window.electronAPI.fileExists === "function"
          ) {
            const videoExists = window.electronAPI.fileExists(videoPath);
            if (videoExists) {
              videoSource = videoPath;
              videoFound = true;
            }
          }
        }

        // Set video source if found
        if (videoFound && videoSource) {
          if (
            typeof videoSource === "string" &&
            (videoSource.startsWith("/") || videoSource.includes(":\\"))
          ) {
            try {
              const buffer = window.electronAPI.readFileBuffer(videoSource);
              const uint8Array = new Uint8Array(buffer);

              const ext = videoSource.toLowerCase().split(".").pop();
              const mimeTypeMap = {
                mp4: "video/mp4",
                mov: "video/quicktime",
                avi: "video/x-msvideo",
                mkv: "video/x-matroska",
                webm: "video/webm",
              };
              const mimeType = mimeTypeMap[ext] || "video/mp4";

              const blob = new Blob([uint8Array], { type: mimeType });
              const fileName = videoSource.split(/[\\/]/).pop();
              const file = new File([blob], fileName, { type: mimeType });

              videoPlayerProps.setVideoSource(file);
              setVideoPathProps(file); // This might need to be updated to store the original path
            } catch (error) {
              const fileUrl = videoSource.startsWith("file://")
                ? videoSource
                : `file://${videoSource}`;

              try {
                const videoElement = document.createElement("video");
                videoElement.src = fileUrl;
                videoPlayerProps.setVideoSource(fileUrl);
                setVideoPathProps({ path: videoSource, name: project.video }); // Store both path and name
              } catch (urlError) {
                alert(`Failed to load video from: ${videoSource}`);
              }
            }
          } else {
            videoPlayerProps.setVideoSource(videoSource);
          }
        }

        // Load segments if they exist in the project
        if (project.segments && Array.isArray(project.segments)) {
          setSegments(project.segments);
          if (project.segments.length > 0) {
            setSelectedSegment(project.segments[0]);
          }
        }
      } catch (error) {
        console.error("❌ Failed to open project:", error);
        alert(`Failed to open project file: ${error.message}`);
      }
    };

    if (
      window.electronAPI &&
      typeof window.electronAPI.onMenuOpenProject === "function"
    ) {
      window.electronAPI.onMenuOpenProject(handleOpenProject);

      return () => {
        if (
          window.electronAPI &&
          typeof window.electronAPI.removeAllListeners === "function"
        ) {
          window.electronAPI.removeAllListeners("menu-open-project");
        }
      };
    }
  }, [videoPlayerProps]);

  return (
    <>
      <ImportMediaModal />
      <ExportModal />
      <ExportMarkersModal />

      <div className="flex flex-col items-center justify-between gap-5 w-full min-h-screen">
        <div
          className={`flex justify-center ${
            videoPlayerProps.videoSrc && page === "home"
              ? "items-end"
              : "items-center"
          } grow`}
        >
          <div className="flex lg:gap-6 xl:gap-10 items-start flex-row shrink-0 w-full justify-center">
            {page == "help" ? (
              <Help />
            ) : (
              <VideoContainer
                videoSrc={videoPlayerProps.videoSrc}
                videoPlayerProps={videoPlayerProps}
                videoPathProps={videoPlayerProps.videoFile}
                dragDropProps={dragDropProps}
                onVideoFile={handleVideoFile}
                selectedSegment={selectedSegment}
                segments={segments}
              />
            )}
            {videoPlayerProps.videoSrc && (
              <VideoSetting
                videoSrc={videoPlayerProps.videoSrc}
                videoPathProps={videoPlayerProps.videoFile}
                onSegmentUpdate={handleSegmentUpdate}
                projectFilePath={projectFilePath}
                originalVideoPath={videoPathProps?.path || videoPathProps?.name}
                videoFile={videoPathProps}
                setSegments={setSegments}
                currentTime={videoPlayerProps.currentTime}
                duration={videoPlayerProps.duration}
                isPlaying={videoPlayerProps.isPlaying}
                onSeek={videoPlayerProps.handleSeek}
                onPlayPause={videoPlayerProps.togglePlay}
                volume={videoPlayerProps.volume}
                onVolumeChange={videoPlayerProps.handleVolumeChange}
                selectedSegment={selectedSegment}
                onSegmentSelect={handleSegmentSelect}
                onSegmentsChange={handleSegmentsChange}
                segments={segments}
              />
            )}
          </div>
        </div>

        {videoPlayerProps.videoSrc && page == "home" && (
          <div className="timeline-wrapper w-full mt-4">
            <VideoTimeline
              videoSrc={videoPlayerProps.videoSrc}
              originalVideoPath={videoPathProps?.path || videoPathProps?.name}
              videoFile={videoPathProps}
              projectFilePath={projectFilePath}
              currentTime={videoPlayerProps.currentTime}
              duration={videoPlayerProps.duration}
              isPlaying={videoPlayerProps.isPlaying}
              onSeek={videoPlayerProps.handleSeek}
              onPlayPause={videoPlayerProps.togglePlay}
              volume={videoPlayerProps.volume}
              onVolumeChange={videoPlayerProps.handleVolumeChange}
              selectedSegment={selectedSegment}
              onSegmentSelect={handleSegmentSelect}
              onSegmentsChange={handleSegmentsChange}
              segments={segments}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default VideoPlayerWithWaveform;
