import React from "react";

const Button = ({
  children,
  onClick,
  variant,
  customClass,
  disabled = false,
}) => {
  const handleClick = (e) => {
    if (disabled) {
      e.preventDefault();
      return;
    }
    onClick?.(e);
  };

  const getButtonClasses = () => {
    const baseCustomClass = customClass ? customClass : "";

    if (variant === "danger") {
      return `${baseCustomClass} px-6 py-3 transition-colors rounded-[40px] shadow-[inset_1px_1px_1px_0px_rgba(255,255,255,0.31)] inline-flex justify-center items-center gap-2 ${
        disabled
          ? "bg-red-300 cursor-not-allowed opacity-50"
          : "bg-red-400 hover:bg-red-500/80 cursor-pointer"
      }`;
    } else {
      return `${baseCustomClass} px-6 py-3 transition-colors rounded-[40px] shadow-[inset_1px_1px_1px_0px_rgba(255,255,255,0.25)] outline-[0.50px] backdrop-blur-[2px] inline-flex justify-center items-center ${
        disabled
          ? "bg-neutral-600/50 outline-neutral-500/30 cursor-not-allowed opacity-50"
          : "bg-neutral-400/50 hover:bg-neutral-400/30 outline-white/50 hover:outline-white/30 cursor-pointer"
      }`;
    }
  };

  return (
    <button
      onClick={handleClick}
      className={getButtonClasses()}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export default Button;
