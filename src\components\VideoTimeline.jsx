/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
// VideoTimeline.jsx
import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
  memo,
} from "react";
import ThumbnailStrip from "./ThumbnailStrip";
import { Slider } from "@mui/material";

// Memoized VideoSegment component
const VideoSegment = memo(
  ({
    segment,
    duration,
    selectedSegment,
    onSegmentSelect,
    colorClass,
    formatTime,
    index,
  }) => {
    if (!duration) return null;

    // Calculate positions as percentages relative to the timeline content
    const startPercent = (segment.start / duration) * 100;
    const widthPercent = ((segment.end - segment.start) / duration) * 100;

    console.log(`Rendering video segment ${segment.name}:`, {
      start: segment.start,
      end: segment.end,
      duration,
      startPercent: startPercent.toFixed(2) + "%",
      widthPercent: widthPercent.toFixed(2) + "%",
      startTime: formatTime(segment.start),
      endTime: formatTime(segment.end),
      hasReplacedAudio: segment.hasReplacedAudio,
    });

    return (
      <div
        className={`absolute top-0 bottom-0 ${colorClass} opacity-70 border-2 cursor-pointer z-50 ${
          selectedSegment === segment
            ? "border-white shadow-lg"
            : "border-blue-300"
        } ${segment.hasReplacedAudio ? "ring-2 ring-green-400" : ""}`}
        style={{
          left: `${startPercent}%`,
          width: `${widthPercent}%`,
          pointerEvents: "auto",
        }}
        onClick={(e) => {
          e.stopPropagation();
          onSegmentSelect(segment);
        }}
      >
        {/* Segment label */}
        <div className="absolute top-1 left-1 bg-black text-white text-xs px-1 rounded font-bold">
          {segment.name}
        </div>

        {/* Audio replacement indicator */}
        {segment.hasReplacedAudio && (
          <div className="absolute top-1 right-1 bg-green-600 text-white text-xs px-1 rounded">
            🎵 {segment.audioFile?.split(".")[0]?.slice(-8) || "Audio"}
          </div>
        )}

        {/* Resize handles for segment adjustment */}
        {selectedSegment === segment && (
          <>
            <div className="absolute left-0 top-0 bottom-0 w-1 bg-white cursor-w-resize opacity-100"></div>
            <div className="absolute right-0 top-0 bottom-0 w-1 bg-white cursor-e-resize opacity-100"></div>
          </>
        )}
      </div>
    );
  }
);

VideoSegment.displayName = "VideoSegment";

// Memoized AudioSegment component
const AudioSegment = memo(
  ({
    segment,
    duration,
    selectedSegment,
    onSegmentSelect,
    colorClass,
    index,
  }) => {
    if (!duration) return null;

    // Calculate positions as percentages
    const startPercent = (segment.start / duration) * 100;
    const widthPercent = ((segment.end - segment.start) / duration) * 100;

    return (
      <div
        className={`absolute top-0 bottom-0 ${colorClass} cursor-pointer z-50 ${
          selectedSegment === segment
            ? "border-white border-2"
            : "border-blue-300 border-2"
        } ${segment.hasReplacedAudio ? "ring-2 ring-green-400" : "opacity-50"}`}
        style={{
          left: `${startPercent}%`,
          width: `${widthPercent}%`,
          pointerEvents: "auto",
          opacity: segment.hasReplacedAudio ? 0.9 : 0.5,
        }}
        onClick={(e) => {
          e.stopPropagation();
          onSegmentSelect(segment);
        }}
      >
        {/* Waveform visualization for replaced audio - overwrites original */}
        {segment.hasReplacedAudio && segment.waveformData && (
          <div className="absolute inset-0 bg-blue-600 flex items-center overflow-hidden">
            <svg
              width="100%"
              height="100%"
              viewBox="0 0 100 40"
              className="absolute inset-0"
              preserveAspectRatio="none"
            >
              {/* Background fill */}
              <rect width="100" height="40" fill="rgba(37, 99, 235, 0.8)" />

              {/* Waveform pattern */}
              <polyline
                fill="none"
                stroke="rgba(34, 197, 94, 0.9)"
                strokeWidth="0.8"
                vectorEffect="non-scaling-stroke"
                points={segment.waveformData
                  .map((point, i) => {
                    const x = (i / (segment.waveformData.length - 1)) * 100;
                    const y = 20 + point * 15; // Center at 20, scale by 15
                    return `${x},${y}`;
                  })
                  .join(" ")}
              />

              {/* Center line */}
              <line
                x1="0"
                y1="20"
                x2="100"
                y2="20"
                stroke="rgba(34, 197, 94, 0.4)"
                strokeWidth="0.3"
                vectorEffect="non-scaling-stroke"
              />

              {/* Audio replacement indicator text */}
              <text
                x="50"
                y="35"
                textAnchor="middle"
                fontSize="3"
                fill="rgba(255,255,255,0.8)"
              >
                REPLACED AUDIO
              </text>
            </svg>
          </div>
        )}
      </div>
    );
  }
);

AudioSegment.displayName = "AudioSegment";

// Memoized WaveformMask component
const WaveformMask = memo(({ segment, duration }) => {
  if (!segment.hasReplacedAudio || !duration) return null;

  const startPercent = (segment.start / duration) * 100;
  const widthPercent = ((segment.end - segment.start) / duration) * 100;

  return (
    <div
      className="absolute top-0 bottom-0 bg-neutral-700/50 z-10"
      style={{
        left: `${startPercent}%`,
        width: `${widthPercent}%`,
      }}
    />
  );
});

WaveformMask.displayName = "WaveformMask";

const VideoTimeline = ({
  videoSrc,
  currentTime,
  duration,
  onSeek,
  selectedSegment,
  onSegmentSelect,
  onSegmentsChange,
  segments = [],
}) => {
  const waveformCanvasRef = useRef(null);
  const timelineRef = useRef(null);
  const timelineContentRef = useRef(null);
  const [audioData, setAudioData] = useState(null);
  const [hasAudio, setHasAudio] = useState(false);
  const [hoverPosition, setHoverPosition] = useState(null);
  const [hoverTime, setHoverTime] = useState(null);
  const [zoomLevel, setZoomLevel] = useState(5);
  const [committedZoomLevel, setCommittedZoomLevel] = useState(5);
  const [isSliderDragging, setIsSliderDragging] = useState(false);
  const [widthParentThumbnail, setWidthParentThumbnail] = useState(0);
  const widthParentThumbnailRef = useRef();
  const [ThumbnailStripWidth, setThumbnailStripWidth] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const ThumbnailStripRef = useRef();
  const [playheadPosition, setPlayheadPosition] = useState(0);

  // New states for timeline features
  const [selectedTool, setSelectedTool] = useState("Select");
  const [showCustomSegmentPopup, setShowCustomSegmentPopup] = useState(false);
  const [customSegments, setCustomSegments] = useState([
    { id: Date.now(), startTime: "", endTime: "", name: "" },
  ]);

  // Function to format time for input (converts seconds to MM:SS format)
  const formatTimeForInput = (timeInSeconds) => {
    if (!timeInSeconds && timeInSeconds !== 0) return "";
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  };

  // Function to parse time input (converts MM:SS to seconds)
  const parseTimeInput = (timeString) => {
    if (!timeString) return 0;
    const parts = timeString.split(":");
    if (parts.length !== 2) return 0;
    const minutes = parseInt(parts[0]) || 0;
    const seconds = parseInt(parts[1]) || 0;
    return minutes * 60 + seconds;
  };

  // Add new segment input row
  const addNewSegmentRow = () => {
    setCustomSegments((prev) => [
      ...prev,
      {
        id: Date.now() + Math.random(),
        startTime: "",
        endTime: "",
        name: "",
      },
    ]);
  };

  // Remove segment input row
  const removeSegmentRow = (id) => {
    setCustomSegments((prev) => prev.filter((segment) => segment.id !== id));
  };

  // Update segment input
  const updateSegmentInput = (id, field, value) => {
    setCustomSegments((prev) =>
      prev.map((segment) =>
        segment.id === id ? { ...segment, [field]: value } : segment
      )
    );
  };

  // Validate and create custom segments
  const handleCreateCustomSegments = () => {
    if (!duration) {
      alert("Video duration not available");
      return;
    }

    const validSegments = [];
    const errors = [];

    customSegments.forEach((segment, index) => {
      if (!segment.startTime || !segment.endTime) {
        errors.push(
          `Row ${index + 1}: Please fill in both start and end times`
        );
        return;
      }

      const startSeconds = parseTimeInput(segment.startTime);
      const endSeconds = parseTimeInput(segment.endTime);

      if (startSeconds >= endSeconds) {
        errors.push(
          `Row ${index + 1}: End time must be greater than start time`
        );
        return;
      }

      if (startSeconds < 0 || endSeconds > duration) {
        errors.push(
          `Row ${
            index + 1
          }: Times must be within video duration (0 - ${formatTime(duration)})`
        );
        return;
      }

      // Check for overlaps with existing segments
      const hasOverlap = segments.some(
        (existingSegment) =>
          startSeconds < existingSegment.end &&
          endSeconds > existingSegment.start
      );

      if (hasOverlap) {
        errors.push(`Row ${index + 1}: Segment overlaps with existing segment`);
        return;
      }

      const segmentName = segment.name.trim() || `Custom Segment ${index + 1}`;

      validSegments.push({
        id: Date.now() + index,
        name: segmentName,
        start: startSeconds,
        end: endSeconds,
      });
    });

    if (errors.length > 0) {
      alert("Please fix the following errors:\n" + errors.join("\n"));
      return;
    }

    if (validSegments.length === 0) {
      alert("No valid segments to create");
      return;
    }

    // Add new segments to existing ones
    const updatedSegments = [...segments, ...validSegments];
    updateSegments(updatedSegments);

    // Close popup and reset form
    setShowCustomSegmentPopup(false);
    setCustomSegments([
      { id: Date.now(), startTime: "", endTime: "", name: "" },
    ]);

    console.log(`Created ${validSegments.length} custom segments`);
  };

  // Update segments using onSegmentsChange (parent manages segments state)
  const updateSegments = (newSegments) => {
    console.log(
      "VideoTimeline updating segments via onSegmentsChange:",
      newSegments
    );
    if (onSegmentsChange) {
      onSegmentsChange(newSegments);
    }
  };

  // New states for segment creation mode
  const [segmentTemplates] = useState([
    { start: 120, end: 150, name: "Segment A (2:00-2:30)" }, // Fixed timing
    { start: 180, end: 205, name: "Segment B (3:00-3:25)" },
    { start: 210, end: 240, name: "Segment C (3:30-4:00)" },
  ]);
  const [currentSegmentIndex, setCurrentSegmentIndex] = useState(0);

  // Memoized segment colors
  const segmentColors = useMemo(
    () => [
      "bg-blue-600",
      "bg-purple-600",
      "bg-orange-600",
      "bg-pink-600",
      "bg-teal-600",
    ],
    []
  );

  // Memoized formatTime function
  const formatTime = useCallback((timeInSeconds) => {
    if (!timeInSeconds && timeInSeconds !== 0) return "00:00";

    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  }, []);

  // Memoized handleSegmentSelect function
  const handleSegmentSelect = useCallback((segment) => {
    if (onSegmentSelect) {
      onSegmentSelect(segment);
    }
    setSelectedSegment(segment);
    console.log("Selected segment:", segment);
  }, []);

  // Delete selected segment function
  const handleDeleteSelectedSegment = useCallback(() => {
    if (!selectedSegment) {
      console.log("No segment selected for deletion");
      return;
    }

    console.log(`Deleting segment: ${selectedSegment.name}`);

    // Stop any audio that might be playing from this segment
    if (selectedSegment.hasReplacedAudio && selectedSegment.audioElement) {
      selectedSegment.audioElement.pause();
      selectedSegment.audioElement.currentTime = 0;
    }

    // Remove the segment using parent's update function
    const updatedSegments = segments.filter(
      (segment) => segment.id !== selectedSegment.id
    );
    updateSegments(updatedSegments);

    // Clear the selection
    if (onSegmentSelect) {
      onSegmentSelect(null);
    }

    console.log(`Successfully deleted segment: ${selectedSegment.name}`);
  }, [selectedSegment, segments]);

  // Add keyboard event listener - place this useEffect with your other useEffects
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Only handle Delete/Backspace when a segment is selected and not typing in an input
      if (
        !selectedSegment ||
        event.target.tagName === "INPUT" ||
        event.target.tagName === "TEXTAREA"
      ) {
        return;
      }

      if (event.key === "Delete" || event.key === "Backspace") {
        event.preventDefault(); // Prevent browser back navigation on Backspace
        handleDeleteSelectedSegment();
      }
    };

    // Add event listener to document
    document.addEventListener("keydown", handleKeyDown);

    // Cleanup
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [selectedSegment, handleDeleteSelectedSegment]);

  useEffect(() => {
    if (ThumbnailStripRef.current) {
      setThumbnailStripWidth(ThumbnailStripRef.current.offsetWidth);
    }
  }, [committedZoomLevel]);

  useEffect(() => {
    setWidthParentThumbnail(widthParentThumbnailRef.current.offsetWidth);
  }, [widthParentThumbnail]);

  // Calculate playhead position based on current time
  useEffect(() => {
    if (!duration || !timelineContentRef.current) return;

    const contentWidth = timelineContentRef.current.clientWidth;
    const timePositionInContent = (currentTime / duration) * contentWidth;
    setPlayheadPosition(timePositionInContent);
  }, [currentTime, duration, zoomLevel, committedZoomLevel]);

  // Extract audio data for waveform
  useEffect(() => {
    const extractAudioData = async () => {
      if (!videoSrc) return;

      try {
        const audioContext = new (window.AudioContext ||
          window.webkitAudioContext)();
        const isElectron = navigator.userAgent
          .toLowerCase()
          .includes("electron");
        let arrayBuffer;

        if (isElectron && window.electronAPI?.readFileBuffer) {
          if (
            typeof videoSrc === "string" &&
            !videoSrc.startsWith("blob:") &&
            !videoSrc.startsWith("data:")
          ) {
            try {
              arrayBuffer = await window.electronAPI.readFileBuffer(videoSrc);
            } catch (fileError) {
              const response = await fetch(videoSrc);
              arrayBuffer = await response.arrayBuffer();
            }
          } else {
            const response = await fetch(videoSrc);
            arrayBuffer = await response.arrayBuffer();
          }
        } else {
          const response = await fetch(videoSrc);
          arrayBuffer = await response.arrayBuffer();
        }

        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        const rawData = audioBuffer.getChannelData(0);
        const hasAudioContent = rawData.some(
          (sample) => Math.abs(sample) > 0.01
        );
        setHasAudio(hasAudioContent);

        const samples = 200;
        const blockSize = Math.floor(rawData.length / samples);
        const filteredData = [];

        for (let i = 0; i < samples; i++) {
          const blockStart = blockSize * i;
          let sum = 0;
          for (let j = 0; j < blockSize; j++) {
            sum += Math.abs(rawData[blockStart + j]);
          }
          filteredData.push(sum / blockSize);
        }

        const multiplier = 1.0 / Math.max(...filteredData, 0.01);
        const normalizedData = filteredData.map((n) => n * multiplier);

        setAudioData(normalizedData);
      } catch (error) {
        console.error("Error extracting audio data:", error);
        setAudioData(new Array(200).fill(0.1));
        setHasAudio(false);
      }
    };

    extractAudioData();
  }, [videoSrc]);

  // Apply zoom level to timeline content
  useEffect(() => {
    if (timelineContentRef.current && duration) {
      const zoomFactor = 1 + (zoomLevel / 100) * 9;
      timelineContentRef.current.style.width = `${zoomFactor * 100}%`;
      ensurePlayheadVisible();
    }
  }, [zoomLevel, duration]);

  // Ensure playhead is visible in the viewport
  const ensurePlayheadVisible = () => {
    if (!timelineRef.current || !duration || !timelineContentRef.current)
      return;

    const container = timelineRef.current;
    const containerWidth = container.clientWidth;
    const contentWidth = timelineContentRef.current.clientWidth;
    const playheadPos = (currentTime / duration) * contentWidth;
    const scrollLeft = container.scrollLeft;
    const scrollRight = scrollLeft + containerWidth;

    if (playheadPos < scrollLeft || playheadPos > scrollRight) {
      container.scrollLeft = playheadPos - containerWidth / 2;
    }
  };

  useEffect(() => {
    ensurePlayheadVisible();
  }, [currentTime]);

  // Draw the waveform
  useEffect(() => {
    if (audioData && waveformCanvasRef.current) {
      const canvas = waveformCanvasRef.current;
      const ctx = canvas.getContext("2d");
      const width = canvas.width;
      const height = canvas.height;

      ctx.clearRect(0, 0, width, height);
      ctx.fillStyle = hasAudio ? "#A2A2A2" : "#999";

      const barWidth = width / audioData.length;

      audioData.forEach((value, index) => {
        const barHeight = value * height * 0.8;
        const x = index * barWidth;
        const y = (height - barHeight) / 2;

        ctx.fillRect(x, y, barWidth - 1, barHeight);
      });
    }
  }, [audioData, hasAudio]);

  // Handle canvas resizing
  useEffect(() => {
    const resizeWaveformCanvas = () => {
      if (waveformCanvasRef.current) {
        const canvas = waveformCanvasRef.current;
        const container = canvas.parentElement;
        canvas.width = container.clientWidth;
        canvas.height = container.clientHeight;

        if (audioData) {
          const ctx = canvas.getContext("2d");
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          ctx.fillStyle = hasAudio ? "#A2A2A2" : "#999";

          const barWidth = canvas.width / audioData.length;

          audioData.forEach((value, index) => {
            const barHeight = value * canvas.height * 0.8;
            const x = index * barWidth;
            const y = (canvas.height - barHeight) / 2;

            ctx.fillRect(x, y, barWidth - 1, barHeight);
          });
        }
      }
    };

    resizeWaveformCanvas();
    window.addEventListener("resize", resizeWaveformCanvas);

    return () => {
      window.removeEventListener("resize", resizeWaveformCanvas);
    };
  }, [audioData, hasAudio]);

  // Handle timeline clicks to seek
  const handleTimelineClick = (e) => {
    if (!duration || !timelineContentRef.current || !timelineRef.current)
      return;

    const container = timelineRef.current;
    const content = timelineContentRef.current;
    const containerRect = container.getBoundingClientRect();
    const clickXInContainer = e.clientX - containerRect.left;
    const clickXInContent = clickXInContainer + container.scrollLeft;
    const contentWidth = content.clientWidth;
    const clampedClickX = Math.max(0, Math.min(clickXInContent, contentWidth));
    const clickPosition = clampedClickX / contentWidth;
    const newTime = Math.max(0, Math.min(clickPosition * duration, duration));

    onSeek(newTime);
  };

  // Handle timeline hover
  const handleTimelineHover = (e) => {
    if (!duration || !timelineContentRef.current || !timelineRef.current)
      return;

    const container = timelineRef.current;
    const content = timelineContentRef.current;
    const containerRect = container.getBoundingClientRect();
    const hoverXInContainer = e.clientX - containerRect.left;
    const hoverXInContent = hoverXInContainer + container.scrollLeft;
    const contentWidth = content.clientWidth;
    const clampedHoverX = Math.max(0, Math.min(hoverXInContent, contentWidth));
    const hoverPosPixels = hoverXInContainer;
    const hoverTimeValue = (clampedHoverX / contentWidth) * duration;

    setHoverPosition(hoverPosPixels);
    setHoverTime(hoverTimeValue);
  };

  // Handle slider change
  const handleZoomChange = useCallback((event, newValue) => {
    const roundedValue = Math.round(newValue);
    setZoomLevel(roundedValue);
    setIsSliderDragging(true);
  }, []);

  const handleZoomChangeCommitted = useCallback((event, newValue) => {
    const roundedValue = Math.round(newValue);
    setCommittedZoomLevel(roundedValue);
    setIsSliderDragging(false);
  }, []);

  const handleZoomIn = () => {
    const newValue = Math.min(zoomLevel + 10, 50);
    setZoomLevel(newValue);
    setCommittedZoomLevel(newValue);
  };

  const handleZoomOut = () => {
    const newValue = Math.max(zoomLevel - 10, 10);
    setZoomLevel(newValue);
    setCommittedZoomLevel(newValue);
  };

  // Handle segments change - now just calls parent update
  const handleSegmentsChange = (newSegments) => {
    console.log("handleSegmentsChange called with:", newSegments);
    console.log("Current segments before change:", segments);
    updateSegments(newSegments);
    console.log("Segments updated via handleSegmentsChange:", newSegments);
  };

  // Clear all segments
  const handleClearAllSegments = () => {
    updateSegments([]);
    if (onSegmentSelect) {
      onSegmentSelect(null);
    }
    console.log("Cleared all segments");
  };

  // New segment creation functions
  const handleCreateSegment = () => {
    const template = segmentTemplates[currentSegmentIndex];

    console.log("=== Creating Segment ===");
    console.log("Template:", template);
    console.log("Duration:", duration);
    console.log("Current segments before creation:", segments);

    // Check if the times are within video duration
    if (!duration) {
      console.error("Video duration not available");
      return;
    }

    const validStartTime = Math.min(template.start, duration);
    const validEndTime = Math.min(template.end, duration);

    if (validStartTime >= validEndTime) {
      console.error(
        "Cannot create segment: End time must be greater than start time"
      );
      return;
    }

    // Check if segment already exists
    const existingSegment = segments.find(
      (seg) => seg.start === validStartTime && seg.end === validEndTime
    );

    if (existingSegment) {
      console.log(`Segment already exists: ${existingSegment.name}`);
      return;
    }

    const newSegment = {
      id: Date.now(),
      name: template.name,
      start: validStartTime,
      end: validEndTime,
    };

    console.log("New segment object:", newSegment);

    // Use functional update to ensure we get the latest state
    const updatedSegments = [...segments, newSegment];
    console.log("Setting segments to:", updatedSegments);
    updateSegments(updatedSegments);

    console.log(
      `Created segment: ${newSegment.name} from ${formatTime(
        validStartTime
      )} to ${formatTime(validEndTime)}`
    );
  };

  const handleCreateAllSegments = () => {
    if (!duration) {
      console.error("Video duration not available");
      return;
    }

    const newSegments = [];

    segmentTemplates.forEach((template, index) => {
      const validStartTime = Math.min(template.start, duration);
      const validEndTime = Math.min(template.end, duration);

      if (validStartTime < validEndTime) {
        // Check if segment doesn't already exist
        const existingSegment = segments.find(
          (seg) => seg.start === validStartTime && seg.end === validEndTime
        );

        if (!existingSegment) {
          newSegments.push({
            id: Date.now() + index,
            name: template.name,
            start: validStartTime,
            end: validEndTime,
          });
        }
      }
    });

    if (newSegments.length === 0) {
      console.log("All segments already exist or are invalid");
      return;
    }

    const updatedSegments = [...segments, ...newSegments];
    updateSegments(updatedSegments);
    console.log(`Created ${newSegments.length} new segments`);
  };

  const handleNextSegment = () => {
    setCurrentSegmentIndex((prev) => (prev + 1) % segmentTemplates.length);
  };

  // Tool menu items
  const menuItems = [
    {
      label: "Select",
      icon: "cursor.svg",
      shortcut: "V",
    },
    {
      label: "Cut",
      icon: "scissor.svg",
      shortcut: "C",
    },
    {
      label: "Hand",
      icon: "hand.svg",
      shortcut: "H",
    },
  ];

  const [valueSelect, setValueSelect] = useState("Select");
  const [iconSelect, setIconSelect] = useState("cursor.svg");
  const [showSelect, setShowSelect] = useState(false);

  const handleToolChange = (label, icon) => {
    setValueSelect(label);
    setIconSelect(icon);
    setSelectedTool(label);
    setShowSelect(false);
  };

  // Memoized time markers to prevent infinite loops
  const timeMarkers = useMemo(() => {
    if (!duration) return [];

    const markers = [];
    let interval = 5; // Default 5 second intervals

    // Adjust interval based on duration for better readability
    if (duration > 3600) interval = 300; // 5 minutes for videos > 1 hour
    else if (duration > 1800)
      interval = 120; // 2 minutes for videos > 30 minutes
    else if (duration > 600) interval = 60; // 1 minute for videos > 10 minutes
    else if (duration > 300) interval = 30; // 30 seconds for videos > 5 minutes
    else if (duration > 120) interval = 15; // 15 seconds for videos > 2 minutes
    else if (duration > 60) interval = 10; // 10 seconds for videos > 1 minute
    else if (duration > 30) interval = 5; // 5 seconds for videos > 30 seconds
    else interval = 2; // 2 seconds for short videos

    // Generate markers at exact intervals
    for (let time = 0; time <= duration; time += interval) {
      const position = (time / duration) * 100;
      markers.push({
        time: Math.min(time, duration), // Ensure we don't exceed duration
        position,
      });
    }

    // Always add a marker at the very end if it doesn't exist
    const lastMarkerTime = markers[markers.length - 1]?.time || 0;
    if (lastMarkerTime < duration) {
      markers.push({
        time: duration,
        position: 100,
      });
    }

    console.log(
      "Generated time markers (memoized):",
      markers.map((m) => ({
        time: formatTime(m.time),
        position: m.position.toFixed(2) + "%",
      }))
    );

    return markers;
  }, [duration, formatTime]); // Added formatTime dependency

  return (
    <>
      {/* Top toolbar */}
      <div className="flex justify-between items-center mb-4">
        {/* Tool selector */}
        <div className="relative z-10 ml-24">
          <div
            className="p-2 inline-flex justify-center items-center gap-2 cursor-pointer select-none bg-neutral-700 rounded-md hover:bg-neutral-600 transition-colors"
            onClick={() => setShowSelect(!showSelect)}
          >
            <div className="w-4 h-4 relative overflow-hidden">
              <img
                src={`./icon/${iconSelect}`}
                alt={valueSelect}
                className="w-full h-full"
              />
            </div>
            <div className="text-center text-white text-base font-normal leading-snug tracking-tight">
              {valueSelect}
            </div>
            <div className="w-4 h-4 relative overflow-hidden">
              <img
                src="./icon/caret-down.svg"
                alt="arrow down"
                className="w-full h-full"
              />
            </div>
          </div>
          {showSelect && (
            <div className="absolute top-12 left-0 shadow-lg z-50">
              <div className="w-44 p-2 bg-neutral-700 rounded-md inline-flex flex-col justify-start items-start">
                {menuItems.map((item, index) => (
                  <div
                    key={index}
                    onClick={() => handleToolChange(item.label, item.icon)}
                    className="self-stretch px-4 py-2 bg-neutral-700 hover:bg-neutral-600 rounded-sm inline-flex justify-between items-center cursor-pointer w-full transition-colors"
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 relative overflow-hidden">
                        <img
                          src={`./icon/${item.icon}`}
                          alt={item.label}
                          className="w-full h-full"
                        />
                      </div>
                      <div className="text-white text-sm font-normal leading-tight tracking-tight select-none">
                        {item.label}
                      </div>
                    </div>
                    <div className="text-zinc-500 text-sm font-medium leading-snug tracking-tight">
                      {item.shortcut}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Segment generation buttons and zoom controls */}
        <div className="flex items-center gap-4">
          {/* Create Individual Segment Button */}
          <button
            onClick={handleCreateSegment}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors flex items-center gap-2"
          >
            <div className="w-4 h-4">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z" />
              </svg>
            </div>
            {segmentTemplates[currentSegmentIndex].name}
          </button>

          {/* Next Segment Button */}
          <button
            onClick={handleNextSegment}
            className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors"
            title="Switch to next segment template"
          >
            <div className="w-4 h-4">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
            </div>
          </button>

          {/* Create All Segments Button */}
          <button
            onClick={() => setShowCustomSegmentPopup(true)}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors flex items-center gap-2"
          >
            <div className="w-4 h-4">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
              </svg>
            </div>
            Create Segments
          </button>

          {/* Clear All Segments Button */}
          {segments.length > 0 && (
            <button
              onClick={handleClearAllSegments}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors flex items-center gap-2"
            >
              <div className="w-4 h-4">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                </svg>
              </div>
              Clear All
            </button>
          )}

          {/* Zoom controls */}
          <div className="flex justify-between items-center gap-1 mr-10">
            <button
              disabled={isGenerating}
              className="w-8 h-8 flex items-center justify-center hover:bg-neutral-700 rounded transition-colors"
              onClick={handleZoomOut}
            >
              <div className="w-4 h-4 relative overflow-hidden mb-1">
                <img
                  src="./icon/zoom-out.svg"
                  alt="zoom out"
                  className="w-full h-full"
                />
              </div>
            </button>
            <div className="w-[160px]">
              <Slider
                value={zoomLevel}
                onChange={handleZoomChange}
                onChangeCommitted={handleZoomChangeCommitted}
                aria-label="Zoom"
                style={{ color: "#D9D9D9" }}
                size="small"
                min={3}
                max={50}
              />
            </div>
            <button
              disabled={isGenerating}
              className="w-8 h-8 flex items-center justify-center mb-1 hover:bg-neutral-700 rounded transition-colors"
              onClick={handleZoomIn}
            >
              <div className="w-4 h-4 relative overflow-hidden">
                <img
                  src="./icon/zoom-in.svg"
                  alt="zoom in"
                  className="w-full h-full"
                />
              </div>
            </button>
          </div>
        </div>
      </div>

      <div className="video-timeline-container bg-neutral-800 p-2 rounded-lg grow shrink-0">
        {/* Timeline container */}
        <div className="timeline-container">
          {/* Main timeline content */}
          <div className="timeline-tracks-container relative">
            <div className="flex items-stretch justify-start ">
              <div className="shrink-0 flex-col justify-center items-center pr-4">
                <div className="w-20 shrink-0 h-8"></div>
                <div className="h-[80px] flex justify-center items-center">
                  <div className="w-20 px-3 bg-zinc-700 rounded-2xl backdrop-blur-[2px] inline-flex justify-center items-center gap-2 ">
                    <div className="justify-start text-white text-sm font-normal leading-9 tracking-tight">
                      Video 1
                    </div>
                  </div>
                </div>
                <div className="h-[80px] flex justify-center items-center">
                  <div className="w-20 px-3 bg-zinc-700 rounded-2xl backdrop-blur-[2px] inline-flex justify-center items-center gap-2 ">
                    <div className="justify-start text-white text-sm font-normal  leading-9 tracking-tight">
                      Audio 1
                    </div>
                  </div>
                </div>
              </div>

              {/* Scrollable timeline */}
              <div
                ref={widthParentThumbnailRef}
                className="timeline-scroll-wrapper shrink-0 grow"
              >
                <div
                  ref={timelineRef}
                  className="timeline-scroll-container relative overflow-x-auto"
                  style={{
                    scrollbarWidth: "thin",
                    scrollbarColor: "#7f7f7f #333333",
                  }}
                >
                  {(isGenerating || isSliderDragging) && (
                    <div className="absolute top-8 left-0 right-0 h-[80px] z-10 flex items-center">
                      <div
                        className={`text-white text-sm font-medium`}
                        style={{
                          transform: timelineRef.current
                            ? `translateX(${timelineRef.current.scrollLeft}px)`
                            : "none",
                          paddingLeft: Math.ceil(widthParentThumbnail / 2),
                        }}
                      >
                        {isSliderDragging
                          ? "Adjusting zoom..."
                          : "Loading thumbnails..."}
                      </div>
                    </div>
                  )}
                  <div
                    ref={timelineContentRef}
                    className="timeline-content relative"
                    onClick={handleTimelineClick}
                    onMouseMove={handleTimelineHover}
                    onMouseLeave={() => {
                      setHoverPosition(null);
                      setHoverTime(null);
                    }}
                  >
                    {/* Time markers */}
                    <div className="grow h-8 w-full flex items-center relative">
                      {timeMarkers.map((marker, i) => (
                        <div
                          key={i}
                          className="absolute flex flex-col items-center"
                          style={{
                            left: `${marker.position}%`,
                            transform: "translateX(-50%)",
                          }}
                        >
                          <div className="h-2 w-px bg-neutral-600"></div>
                          <div className="text-xs text-neutral-400 whitespace-nowrap">
                            {formatTime(marker.time)}
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Thumbnail strip with enhanced features */}
                    <div
                      ref={ThumbnailStripRef}
                      className="thumbnail-strip relative h-[76px] bg-neutral-700/50 rounded-md overflow-hidden grow shrink-0"
                    >
                      <ThumbnailStrip
                        videoSrc={videoSrc}
                        duration={duration}
                        currentTime={currentTime}
                        hoverTime={hoverTime}
                        hoverPosition={hoverPosition}
                        zoomLevel={committedZoomLevel}
                        isSliderDragging={isSliderDragging}
                        onGeneratingChange={(generating) => {
                          setIsGenerating(generating);
                        }}
                        onTimeChange={onSeek}
                        segments={segments}
                        onSegmentSelect={handleSegmentSelect}
                      />

                      {/* Video segments overlay on thumbnails - Now memoized */}
                      {segments.map((segment, index) => (
                        <VideoSegment
                          key={`video-segment-${segment.id}`}
                          segment={segment}
                          duration={duration}
                          selectedSegment={selectedSegment} // This now comes from props
                          onSegmentSelect={handleSegmentSelect}
                          colorClass={
                            segmentColors[index % segmentColors.length]
                          }
                          formatTime={formatTime}
                          index={index}
                        />
                      ))}

                      {/* Current time indicator */}
                      <div
                        className="current-time-indicator absolute top-0 bottom-0 w-0.5 bg-white z-40 pointer-events-none"
                        style={{
                          left: `${playheadPosition}px`,
                          transform: "translateX(-50%)",
                        }}
                      />
                    </div>

                    {/* Audio waveform */}
                    <div className="waveform-container relative h-[76px] bg-neutral-700/50 rounded-md overflow-hidden mt-2 grow shrink-0">
                      <canvas
                        ref={waveformCanvasRef}
                        className="waveform-canvas absolute inset-0 w-full h-full"
                      />

                      {/* Mask to hide original waveform where we have replaced audio - Now memoized */}
                      {segments.map((segment) => (
                        <WaveformMask
                          key={`waveform-mask-${segment.id}`}
                          segment={segment}
                          duration={duration}
                        />
                      ))}

                      {/* Playhead */}
                      <div
                        className="playhead absolute top-0 bottom-0 w-0.5 bg-white z-40 pointer-events-none"
                        style={{
                          left: `${playheadPosition}px`,
                          transform: "translateX(-50%)",
                        }}
                      />

                      {/* Audio segments overlay - Now memoized */}
                      {segments.map((segment, index) => (
                        <AudioSegment
                          key={`audio-segment-${segment.id}`}
                          segment={segment}
                          duration={duration}
                          selectedSegment={selectedSegment}
                          onSegmentSelect={handleSegmentSelect}
                          colorClass={
                            segmentColors[index % segmentColors.length]
                          }
                          index={index}
                        />
                      ))}

                      {!hasAudio && (
                        <div className="no-audio-message absolute inset-0 flex items-center justify-center text-neutral-400 text-sm z-5">
                          No audio detected in this video
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Popup crete segments */}
      {showCustomSegmentPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-neutral-800 rounded-lg p-6 w-[600px] max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-white">
                Create Custom Segments
              </h2>
              <button
                onClick={() => setShowCustomSegmentPopup(false)}
                className="text-gray-400 hover:text-white"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="mb-4">
              <p className="text-gray-300 text-sm">
                Video Duration: {formatTime(duration || 0)} | Format: MM:SS
                (e.g., 02:30 for 2 minutes 30 seconds)
              </p>
            </div>

            <div className="space-y-3 mb-4">
              {customSegments.map((segment, index) => (
                <div
                  key={segment.id}
                  className="flex items-center gap-3 p-3 bg-neutral-700 rounded-md"
                >
                  <div className="text-white text-sm font-medium w-12">
                    #{index + 1}
                  </div>

                  <div className="flex-1">
                    <input
                      type="text"
                      placeholder="Segment Name (optional)"
                      value={segment.name}
                      onChange={(e) =>
                        updateSegmentInput(segment.id, "name", e.target.value)
                      }
                      className="w-full px-3 py-2 bg-neutral-600 text-white rounded border border-neutral-500 focus:border-blue-500 focus:outline-none"
                    />
                  </div>

                  <div className="flex items-center gap-2">
                    <div>
                      <label className="block text-xs text-gray-400 mb-1">
                        Start Time
                      </label>
                      <input
                        type="text"
                        placeholder="00:00"
                        pattern="[0-9]{2}:[0-9]{2}"
                        value={segment.startTime}
                        onChange={(e) =>
                          updateSegmentInput(
                            segment.id,
                            "startTime",
                            e.target.value
                          )
                        }
                        className="w-20 px-2 py-1 bg-neutral-600 text-white rounded text-center border border-neutral-500 focus:border-blue-500 focus:outline-none"
                      />
                    </div>

                    <div className="text-gray-400">to</div>

                    <div>
                      <label className="block text-xs text-gray-400 mb-1">
                        End Time
                      </label>
                      <input
                        type="text"
                        placeholder="00:00"
                        pattern="[0-9]{2}:[0-9]{2}"
                        value={segment.endTime}
                        onChange={(e) =>
                          updateSegmentInput(
                            segment.id,
                            "endTime",
                            e.target.value
                          )
                        }
                        className="w-20 px-2 py-1 bg-neutral-600 text-white rounded text-center border border-neutral-500 focus:border-blue-500 focus:outline-none"
                      />
                    </div>
                  </div>

                  {customSegments.length > 1 && (
                    <button
                      onClick={() => removeSegmentRow(segment.id)}
                      className="text-red-400 hover:text-red-300 p-1"
                      title="Remove this segment"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>
                  )}
                </div>
              ))}
            </div>

            <div className="flex items-center justify-between mb-6">
              <button
                onClick={addNewSegmentRow}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors flex items-center gap-2"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                Add Another Segment
              </button>

              <div className="text-sm text-gray-400">
                {customSegments.length} segment
                {customSegments.length !== 1 ? "s" : ""} to create
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowCustomSegmentPopup(false)}
                className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateCustomSegments}
                className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors"
                disabled={customSegments.every(
                  (seg) => !seg.startTime || !seg.endTime
                )}
              >
                Create Segments
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default VideoTimeline;
