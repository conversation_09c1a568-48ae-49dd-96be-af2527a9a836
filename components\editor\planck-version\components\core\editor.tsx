import React from "react";
import { VideoPlayer } from "./video-player";
import { Timeline } from "./timeline";
import { useEditorContext } from "../../contexts/editor-context";
import { Button } from "@/components/ui/button";
import { Play, Pause, Square, RotateCcw, Save, Upload } from "lucide-react";

/**
 * Main Editor component that orchestrates the video editing interface
 * Includes the video player, timeline, and control panels
 */
export const Editor: React.FC = () => {
  const {
    // Player state
    isPlaying,
    currentFrame,
    playerRef,
    togglePlayPause,

    // Overlays
    overlays,
    setOverlays,
    selectedOverlayId,
    setSelectedOverlayId,
    handleOverlayChange,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,

    // Timeline
    durationInFrames,
    handleTimelineClick,

    // History
    undo,
    redo,
    canUndo,
    canRedo,

    // File handling
    handleFilesAdded,
    isProcessing,

    // Autosave
    saveProject,
  } = useEditorContext();

  const handleStop = () => {
    if (playerRef.current) {
      playerRef.current.pause();
      playerRef.current.seekTo(0);
    }
  };

  const handleReset = () => {
    setOverlays([]);
    if (playerRef.current) {
      playerRef.current.seekTo(0);
    }
  };

  const isEmpty = overlays.length === 0;

  return (
    <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header Controls */}
      <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex items-center gap-2">
          <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Video Editor
          </h1>
          {!isEmpty && (
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {overlays.length} item{overlays.length !== 1 ? "s" : ""}
            </span>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* File Upload Button */}
          <div className="relative">
            <input
              type="file"
              multiple
              accept=".mp4,.mov,.avi,.mkv,.webm,.m4v,.mp3,.wav,.aac,.m4a,.ogg,.flac,.jpg,.jpeg,.png,.gif,.bmp,.webp,.svg"
              onChange={(e) => {
                const files = Array.from(e.target.files || []);
                if (files.length > 0) {
                  handleFilesAdded(files);
                }
                e.target.value = "";
              }}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              disabled={isProcessing}
            />
            <Button
              variant="outline"
              size="sm"
              disabled={isProcessing}
              className="relative"
            >
              <Upload className="h-4 w-4 mr-2" />
              {isProcessing ? "Processing..." : "Upload"}
            </Button>
          </div>

          {/* History Controls */}
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={undo}
              disabled={!canUndo}
              title="Undo (Ctrl+Z)"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={redo}
              disabled={!canRedo}
              title="Redo (Ctrl+Y)"
            >
              <RotateCcw className="h-4 w-4 scale-x-[-1]" />
            </Button>
          </div>

          {/* Save Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={saveProject}
            title="Save Project (Ctrl+S)"
          >
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden">
        {/* Video Player Section */}
        <div className="flex-1 flex flex-col">
          {/* Player Container */}
          <div className="flex-1 p-4">
            <div className="h-full w-full">
              <VideoPlayer
                playerRef={playerRef}
                onFilesAdded={handleFilesAdded}
              />
            </div>
          </div>

          {/* Player Controls */}
          <div className="p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-center gap-4">
              {/* Playback Controls */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleStop}
                  disabled={isEmpty}
                  title="Stop"
                >
                  <Square className="h-4 w-4" />
                </Button>

                <Button
                  variant="default"
                  size="sm"
                  onClick={togglePlayPause}
                  disabled={isEmpty}
                  title={isPlaying ? "Pause (Space)" : "Play (Space)"}
                  className="min-w-[80px]"
                >
                  {isPlaying ? (
                    <>
                      <Pause className="h-4 w-4 mr-2" />
                      Pause
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Play
                    </>
                  )}
                </Button>
              </div>

              {/* Frame Info */}
              {!isEmpty && (
                <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                  <span>
                    Frame: {currentFrame} / {durationInFrames}
                  </span>
                  <span>
                    Duration: {Math.round((durationInFrames / 30) * 100) / 100}s
                  </span>
                </div>
              )}

              {/* Reset Button */}
              {!isEmpty && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReset}
                  title="Clear all content"
                  className="ml-auto"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Timeline Section */}
      <div className="h-64 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 shadow-lg">
        <Timeline
          overlays={overlays}
          durationInFrames={durationInFrames || 900} // Default 30 seconds for empty timeline
          selectedOverlayId={selectedOverlayId}
          setSelectedOverlayId={setSelectedOverlayId}
          currentFrame={currentFrame}
          onOverlayChange={handleOverlayChange}
          setCurrentFrame={(frame) => {
            if (playerRef.current) {
              playerRef.current.seekTo(frame);
            }
          }}
          onTimelineClick={handleTimelineClick}
          onOverlayDelete={deleteOverlay}
          onOverlayDuplicate={duplicateOverlay}
          onSplitOverlay={splitOverlay}
          setOverlays={setOverlays}
          onFilesAdded={handleFilesAdded} // Pass file handler to timeline as well
        />
      </div>

      {/* Global Keyboard Shortcuts Handler */}
      <GlobalKeyboardShortcuts />
    </div>
  );
};

/**
 * Component to handle global keyboard shortcuts
 */
const GlobalKeyboardShortcuts: React.FC = () => {
  const { togglePlayPause, undo, redo, saveProject, playerRef, overlays } =
    useEditorContext();

  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Prevent shortcuts when typing in inputs
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement
      ) {
        return;
      }

      // Space bar for play/pause
      if (e.code === "Space" && overlays.length > 0) {
        e.preventDefault();
        togglePlayPause();
      }

      // Ctrl/Cmd combinations
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case "z":
            if (e.shiftKey) {
              e.preventDefault();
              redo();
            } else {
              e.preventDefault();
              undo();
            }
            break;
          case "y":
            e.preventDefault();
            redo();
            break;
          case "s":
            e.preventDefault();
            saveProject();
            break;
        }
      }

      // Arrow keys for frame navigation
      if (playerRef.current && overlays.length > 0) {
        switch (e.key) {
          case "ArrowLeft":
            e.preventDefault();
            const currentFrame = playerRef.current.getCurrentFrame();
            playerRef.current.seekTo(Math.max(0, currentFrame - 1));
            break;
          case "ArrowRight":
            e.preventDefault();
            const frame = playerRef.current.getCurrentFrame();
            playerRef.current.seekTo(frame + 1);
            break;
          case "Home":
            e.preventDefault();
            playerRef.current.seekTo(0);
            break;
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [togglePlayPause, undo, redo, saveProject, playerRef, overlays.length]);

  return null;
};
