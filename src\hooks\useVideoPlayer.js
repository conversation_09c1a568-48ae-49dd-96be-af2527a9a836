// hooks/useVideoPlayer.js
import { useState, useRef, useEffect } from "react";

export const useVideoPlayer = (
  initialVideoSrc = null,
  initialVideoFile = null
) => {
  const videoRef = useRef(null);
  const [videoSrc, setVideoSrc] = useState(initialVideoSrc);
  const [videoFile, setVideoFile] = useState(initialVideoFile);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);

  // Clean up object URLs
  useEffect(() => {
    return () => {
      if (videoSrc) {
        URL.revokeObjectURL(videoSrc);
      }
    };
  }, [videoSrc]);

  // Set up clear project listener
  useEffect(() => {
    if (!window.electronAPI) {
      return;
    }

    // Check if onClearProject exists before using it
    if (typeof window.electronAPI.onClearProject === "function") {
      const handleClearProject = () => {
        setVideoSrc(null);
        setVideoFile(null);
        setCurrentTime(0);
        setDuration(0);
        setIsPlaying(false);
      };

      window.electronAPI.onClearProject(handleClearProject);

      // Cleanup
      return () => {
        if (
          window.electronAPI &&
          typeof window.electronAPI.removeAllListeners === "function"
        ) {
          window.electronAPI.removeAllListeners("clear-project");
        }
      };
    } else {
      console.warn("⚠️ electronAPI.onClearProject not available");
    }
  }, []);

  // Update play state when video plays or pauses
  useEffect(() => {
    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const videoCurrent = videoRef.current;

    if (videoCurrent) {
      videoCurrent.addEventListener("play", handlePlay);
      videoCurrent.addEventListener("pause", handlePause);
    }

    return () => {
      if (videoCurrent) {
        videoCurrent.removeEventListener("play", handlePlay);
        videoCurrent.removeEventListener("pause", handlePause);
      }
    };
  }, [videoSrc]);

  useEffect(() => {
    setVideoSrc(initialVideoSrc);
  }, [initialVideoSrc]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      window.__hasUnsavedProject__ = !!videoSrc;
    }
  }, [videoSrc]);

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleVideoLoaded = () => {
    if (videoRef.current) {
      const videoDuration = videoRef.current.duration;
      setDuration(videoDuration);
      videoRef.current.volume = volume;
    }
  };

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleVolumeChange = (e) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
    }
  };

  const handleSeek = (time) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const resetPlayerState = () => {
    setCurrentTime(0);
    setDuration(0);
    setIsPlaying(false);
  };

  const setVideoSource = (source) => {
    // Revoke previous URL if exists
    if (
      videoSrc &&
      typeof videoSrc === "string" &&
      videoSrc.startsWith("blob:")
    ) {
      URL.revokeObjectURL(videoSrc);
    }

    // Handle different types of sources
    if (source instanceof File) {
      // File object - create blob URL
      setVideoFile(source);
      const videoURL = URL.createObjectURL(source);
      setVideoSrc(videoURL);
    } else if (typeof source === "string") {
      // String - could be file path or blob URL
      setVideoFile(null);
      setVideoSrc(source);
    } else {
      // Clear video
      setVideoFile(null);
      setVideoSrc(null);
    }

    resetPlayerState();
  };

  return {
    videoRef,
    videoSrc,
    videoFile,
    isPlaying,
    currentTime,
    duration,
    volume,
    setVideoSource,
    handleTimeUpdate,
    handleVideoLoaded,
    togglePlay,
    handleVolumeChange,
    handleSeek,
    resetPlayerState,
  };
};
