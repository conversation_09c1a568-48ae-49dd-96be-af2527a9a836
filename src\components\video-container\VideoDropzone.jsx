import React, { useRef } from "react";
import { validateVideoFile } from "../../utils/videoValidation";

const VideoDropzone = ({
  dragging,
  isReplacing,
  videoSrc,
  onDragHandlers,
  onVideoFile,
  children,
}) => {
  const fileInputRef = useRef(null);

  const handleVideoFile = (file) => {
    try {
      validateVideoFile(file);
      onVideoFile(file);
    } catch (error) {
      alert(error.message);
    }
  };

  const handleFileChange = (e) => {
    const files = e.target.files;
    if (files && files.length) {
      handleVideoFile(files[0]);
    }
  };

  // const triggerFileInput = () => {
  //   fileInputRef.current.click();
  // };

  const triggerFileInput = async () => {
    if (!window.electronAPI) return;

    try {
      const filePath = await window.electronAPI.openFileDialog();
      if (filePath) {
        const fileData = await window.electronAPI.readFileAsBuffer(filePath);
        const file = new File([fileData], filePath.split(/[\\/]/).pop(), {
          type: "video/mp4"
        });
        handleVideoFile(file);
        window.electronAPI.enableSaveMenu(true);
      }
    } catch (err) {
      console.error("Error reading file:", err);
    }
  };

  return (
    <div
      className={`video-dropzone h-full ${dragging ? "dragging" : ""} ${
        videoSrc ? "has-video lg:max-w-[646px] xl:max-w-[1078px]" : "w-full"
      }`}
      onDragEnter={onDragHandlers.handleDragEnter}
      onDragLeave={onDragHandlers.handleDragLeave}
      onDragOver={onDragHandlers.handleDragOver}
      onDrop={onDragHandlers.handleDrop}
    >
      {children({ triggerFileInput, isReplacing })}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="video/*"
        style={{ display: "none" }}
      />
    </div>
  );
};

export default VideoDropzone;
