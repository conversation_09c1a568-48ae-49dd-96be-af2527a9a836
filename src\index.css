@import "tailwindcss";
@import url("https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,100..700;1,100..700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,100..700;1,100..700&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

@theme {
  --font-inter: "Inter", sans-serif;
  --font-ibm: "IBM Plex Sans", sans-serif;
}
html {
  overflow: hidden;
}
body {
  font-family: "IBM Plex Sans", sans-serif;
  overflow-x: hidden;
  user-select: none;
}
.main-layout {
  background: url("/bg.png") no-repeat;
  background-size: cover;
}
.box-popup {
  backdrop-filter: blur(20px);
  box-shadow: 1px 1px 3px 0px #ffffff4a inset;
}
/* Custom Scrollbar */
.custom-scroll::-webkit-scrollbar {
  width: 8px;
}

.custom-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background-color: #666666;
  border-radius: 4px;
  border: none;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background-color: #888888;
}

.custom-scroll::-webkit-scrollbar-thumb:active {
  background-color: #999999;
}
