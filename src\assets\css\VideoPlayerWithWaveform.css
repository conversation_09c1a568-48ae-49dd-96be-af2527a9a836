/* VideoPlayerWithWaveform.css */

/* Video container */
.video-upload-container {
  position: relative;
  width: 100%;
  background-color: #000;
  overflow: hidden;
}

.video-preview-wrapper {
  position: relative;
  width: 100%;
  background-color: #000;
  overflow: hidden;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.video-preview {
  display: block;
  max-width: 100%;
  margin: 0 auto;
}

/* Video controls overlay */
.video-controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-preview-wrapper:hover .video-controls-overlay {
  opacity: 1;
}

.play-button-overlay {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 24px;
}

/* Timeline wrapper */

.video-timeline {
  background: #2a2a2a;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Thumbnail component */
.thumbnail-component {
  position: relative;
}

/* Thumbnails strip */
.thumbnails-strip {
  display: flex;
  height: 80px;
  background: #333;
  border-radius: 4px 4px 0 0;
  overflow: hidden;
  position: relative;
}

.thumbnail-item {
  height: 100%;
  background-size: cover;
  background-position: center;
  border-right: 1px solid rgba(0, 0, 0, 0.3);
}

.thumbnail-item:last-child {
  border-right: none;
}

.current-time-indicator {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: #ff9800;
  pointer-events: none;
  z-index: 2;
}

/* Waveform container */
.waveform-container {
  position: relative;
  height: 80px;
  background: #333;
  border-radius: 0 0 4px 4px;
  cursor: pointer;
}

.waveform-canvas {
  width: 100%;
  height: 100%;
}

.playhead {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: #ff9800;
  pointer-events: none;
}

.no-audio-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  pointer-events: none;
}

/* Thumbnail preview */
.thumbnail-preview {
  position: absolute;
  bottom: 100%;
  margin-bottom: 10px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  padding: 4px;
  pointer-events: none;
  z-index: 10;
}

.thumbnail-canvas {
  display: block;
  border-radius: 2px;
}

.thumbnail-time {
  text-align: center;
  color: white;
  font-size: 12px;
  margin-top: 4px;
  font-family: monospace;
}

/* Controls bar */
.controls-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 0;
}

.time-display {
  color: white;
  font-family: monospace;
  font-size: 14px;
  margin: 0 10px;
}

.volume-control {
  display: flex;
  align-items: center;
  margin: 0 10px;
}

.volume-icon {
  color: white;
  margin-right: 5px;
  font-size: 14px;
}

.volume-slider {
  width: 80px;
  height: 4px;
  -webkit-appearance: none;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
}

.replace-button {
  background: rgba(231, 76, 60, 0.8);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.replace-button:hover {
  background: rgba(192, 57, 43, 0.9);
}

/* Drag and drop styles */
.video-dropzone.dragging {
  background-color: rgba(52, 152, 219, 0.2);
}

.video-preview-wrapper.replacing::after {
  content: "Drop to replace video";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 24px;
  font-weight: bold;
}

/* Placeholder content */
.placeholder-content {
  cursor: pointer;
}

/* Add these styles to your VideoPlayerWithWaveform.css */

/* Timeline container */
.timeline-with-markers {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Time markers */
.time-markers-container {
  position: relative;
  height: 24px;
  width: calc(100% - 38px);
  left: 20px;
}

.time-marker {
  position: absolute;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2; /* Above dotted lines */
}

.time-marker-label {
  color: #9ca3af; /* Gray-400 */
  font-size: 0.75rem; /* text-xs */
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
  white-space: nowrap;
  background-color: #2a2a2a; /* Match timeline background */
  padding: 0 4px;
}

/* Dotted lines */
.dotted-line {
  position: absolute;
  width: 1px;
  top: 0;
  bottom: 0;
  border-left: 1px dashed rgba(255, 255, 255, 0.2);
  transform: translateX(-50%);
  z-index: 1;
}

/* Thumbnail strip */
.thumbnails-strip {
  display: flex;
  height: 80px;
  background: #333;
  border-radius: 0.5rem; /* rounded-lg */
  overflow: hidden;
  position: relative;
  width: 100%;
}

.thumbnail-item {
  height: 100%;
  background-size: cover;
  background-position: center;
  border-right: 1px solid rgba(0, 0, 0, 0.3);
}

.thumbnail-item:last-child {
  border-right: none;
}

.current-time-indicator {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: #ff9800;
  pointer-events: none;
  z-index: 2;
}

/* Thumbnail preview */
.thumbnail-preview {
  position: absolute;
  bottom: 100%;
  margin-bottom: 10px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  padding: 4px;
  pointer-events: none;
  z-index: 10;
}

.thumbnail-canvas {
  display: block;
  border-radius: 2px;
}

.thumbnail-time {
  text-align: center;
  color: white;
  font-size: 12px;
  margin-top: 4px;
  font-family: monospace;
}
/* Add this to your CSS file */
.timeline-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: #7f7f7f #333333;
}

/* For Webkit browsers (Chrome, Safari) */
.timeline-scroll-container::-webkit-scrollbar {
  height: 8px;
}

.timeline-scroll-container::-webkit-scrollbar-track {
  background: #333333;
  border-radius: 4px;
}

.timeline-scroll-container::-webkit-scrollbar-thumb {
  background-color: #7f7f7f;
  border-radius: 4px;
}

/* Ensure smooth transitions for playhead */
.playhead,
.current-time-indicator,
.hover-indicator,
.hover-time {
  transition: left 0.1s ease-out;
}

/* Ensure the timeline content grows properly with zoom */
.timeline-content {
  min-width: 100%;
  transition: width 0.3s ease-out;
}
