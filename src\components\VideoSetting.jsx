import {
  Box,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Slider,
  TextField,
} from "@mui/material";
import { useEffect, useRef, useState } from "react";
import SelectEnv from "./environtment-criteria/SelectEnv";
import Button from "./Button";
import ItemSetting from "./ItemSetting";
import PopupSetting from "./PopupSetting";
import { CheckedIcon, UncheckedIcon } from "./icon/CutomIcon";
import {
  AudioTypeItems,
  envItem,
  FootwearItems,
  GroundMaterialItems,
  GroundTextureItems,
  VideoTypeItems,
  weatherItems,
} from "../data/VideoSetting";
import { useAppContext } from "../hooks/useAppContext";
import { useSaveProject } from "../hooks/useSaveProject.js";
import { useProjectPath } from "../hooks/useProjectPath.js";

const VideoSetting = ({
  videoSrc,
  videoPathProps,
  selectedSegment,
  onSegmentUpdate,
  segments = [],
  onSegmentsChange,
}) => {
  const [weather, setWeather] = useState("dry");
  const [environtment, setEnvirontment] = useState("indoor");
  const [groundTexture, setGroundTexture] = useState("soft");
  const [groundMaterial, setGroundMaterial] = useState("carpet");
  const [videoType, setVideoType] = useState("mp4");
  const [audioType, setAudioType] = useState("wav");
  const [footwear, setFootwear] = useState("barefoot");
  const [showPopup, setShowPopup] = useState("");
  const [seed, setSeed] = useState(0);
  const [qualitySounds, setQualitySounds] = useState(50);
  const [guidenceStrength, setGuidenceStrength] = useState(7);
  const { setPage } = useAppContext();
  const [isLoading, setIsLoding] = useState(false);
  const [characterSize, setCharacterSize] = useState("big");
  const [fullPrompt, setFullPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState(
    "talking, speech, voice, dialogue, conversation, ambient noise, background noise, room tone, breathing, fabric rustle, music, chatter, murmuring, whispering, echo"
  );
  const [segmentAudioFiles, setSegmentAudioFiles] = useState({});
  const [segmentSettings, setSegmentSettings] = useState({});

  const { saveProject, savedPath } = useSaveProject(videoSrc, videoPathProps);
  const { projectUrl, pathRef, updatePath } = useProjectPath();

  const saveCurrentSettingsForSegment = (segmentId) => {
    const currentSettings = {
      characterSize: characterSize,
      weather: weather,
      environment: environtment,
      groundTexture: groundTexture,
      groundMaterial: groundMaterial,
      footwear: footwear,
      fullPrompt: fullPrompt,
      negativePrompt: negativePrompt,
      seed: seed,
      qualitySounds: qualitySounds,
      guidenceStrength: guidenceStrength,
    };

    setSegmentSettings((prev) => ({
      ...prev,
      [segmentId]: currentSettings,
    }));

    return currentSettings;
  };

  useEffect(() => {
    updatePath(savedPath);
  }, [savedPath, updatePath]);

  // Add formatTime function that was missing
  const formatTime = (timeInSeconds) => {
    if (!timeInSeconds && timeInSeconds !== 0) return "00:00";
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  };

  // 1. Synchronize paths using ref to always get fresh value
  const projectUrlRef = useRef(projectUrl);
  useEffect(() => {
    projectUrlRef.current = projectUrl;
  }, [projectUrl]);

  const handleReplaceSegmentAudio = async (audioFileName) => {
    if (!selectedSegment) {
      console.log("No segment selected for audio replacement");
      return;
    }

    // Use pathRef.current from the useProjectPath hook (already available in component)
    const projectPath = pathRef.current;

    // Check if we have a valid path
    if (!projectPath || typeof projectPath !== "string") {
      console.error(
        "No valid pathRef available for audio replacement:",
        projectPath
      );
      return;
    }

    console.log("Using pathRef from hook:", projectPath);

    const audioPath = `${projectPath}/ai/${audioFileName}`;

    console.log("Constructed audio path:", audioPath);

    try {
      console.log(`Replacing audio for segment: ${selectedSegment.name}`);
      console.log(
        `Segment time range: ${formatTime(
          selectedSegment.start
        )} to ${formatTime(selectedSegment.end)}`
      );
      console.log(`Audio file path: ${audioPath}`);

      // Create audio element to load the replacement audio
      let audio;

      if (
        window.electronAPI &&
        typeof window.electronAPI.readFileBuffer === "function"
      ) {
        try {
          // For Electron environment, read file as buffer first
          console.log("Attempting to read audio file as buffer...");
          const buffer = await window.electronAPI.readFileBuffer(audioPath);
          const blob = new Blob([buffer], { type: "audio/wav" });
          const audioUrl = URL.createObjectURL(blob);
          audio = new Audio(audioUrl);
          console.log("Successfully loaded audio from buffer");
        } catch (fileError) {
          console.warn(
            "Failed to read audio file as buffer, trying direct path:",
            fileError
          );
          // Fallback to direct file path
          const fileUrl = audioPath.startsWith("file://")
            ? audioPath
            : `file://${audioPath}`;
          audio = new Audio(fileUrl);
        }
      } else {
        // For web environment
        audio = new Audio(audioPath);
      }

      // Wait for audio to load
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error("Audio loading timeout"));
        }, 10000); // 10 second timeout

        audio.addEventListener("loadeddata", () => {
          clearTimeout(timeout);
          resolve();
        });
        audio.addEventListener("error", (e) => {
          clearTimeout(timeout);
          reject(e);
        });
        audio.load();
      });

      console.log("Audio loaded successfully:", {
        duration: audio.duration,
        src: audio.src,
      });

      // Generate waveform data for visualization
      const segmentDuration = selectedSegment.end - selectedSegment.start;
      const sampleCount = Math.ceil(segmentDuration * 20);
      const waveformData = [];

      // Generate realistic guitar waveform pattern
      for (let i = 0; i < sampleCount; i++) {
        const time = (i / sampleCount) * segmentDuration;
        const fundamental = Math.sin(time * 2 * Math.PI * 2) * 0.6;
        const harmonic2 = Math.sin(time * 2 * Math.PI * 4) * 0.3;
        const harmonic3 = Math.sin(time * 2 * Math.PI * 6) * 0.2;
        const decay = Math.exp(-time * 0.5);
        const variation = (Math.random() - 0.5) * 0.1;
        const amplitude =
          (fundamental + harmonic2 + harmonic3) * decay + variation;
        waveformData.push(Math.max(-1, Math.min(1, amplitude)));
      }

      // Set up audio to sync with video playback - IMPROVED LOOPING
      audio.loop = true;
      audio.preload = "auto"; // Ensure audio is preloaded for smooth looping

      // Add event listener for seamless looping
      audio.addEventListener("ended", () => {
        audio.currentTime = 0;
        if (!audio.paused) {
          audio.play();
        }
      });

      // Update segment with replaced audio data - ADD THE MISSING PROPERTIES HERE
      const updatedSegment = {
        ...selectedSegment,
        hasReplacedAudio: true,
        audioFile: audioFileName,
        audioPath: audioPath,
        audioElement: audio,
        waveformData: waveformData,
        audioDuration: audio.duration,
        shouldMuteOriginal: true,
        audioOverride: true,
        originalAudioMuted: true,
      };

      // Update segments using onSegmentsChange if available, otherwise use onSegmentUpdate
      if (onSegmentsChange && segments) {
        // Use onSegmentsChange to update the entire segments array (like VideoTimeline does)
        const updatedSegments = segments.map((seg) =>
          seg.id === selectedSegment.id ? updatedSegment : seg
        );
        onSegmentsChange(updatedSegments);
        console.log("✅ Audio replacement completed successfully");
      } else if (onSegmentUpdate) {
        // Fallback to individual segment update
        onSegmentUpdate(updatedSegment);
        console.log("✅ Audio replacement completed successfully");
      }

      console.log(
        `Audio successfully replaced for "${selectedSegment.name}". Audio will play automatically when video playhead enters this segment.`
      );

      return { success: true, updatedSegment };
    } catch (error) {
      console.error("Error replacing audio:", error);
      console.error(
        `Failed to replace audio: ${error.message}. Make sure the file exists at: ${audioPath}`
      );

      // Additional debugging information
      if (
        window.electronAPI &&
        typeof window.electronAPI.fileExists === "function"
      ) {
        const exists = window.electronAPI.fileExists(audioPath);
        console.log(`File exists check for ${audioPath}: ${exists}`);

        // Try to list files in the ai directory
        try {
          const separator = projectPath.includes("\\") ? "\\" : "/";
          const projectDir = projectPath.substring(
            0,
            projectPath.lastIndexOf(separator)
          );
          const aiDir = `${projectDir}${separator}ai`;
          console.log(`Checking ai directory: ${aiDir}`);

          if (
            window.electronAPI.directoryExists &&
            window.electronAPI.directoryExists(aiDir)
          ) {
            console.log("AI directory exists");
            if (window.electronAPI.listFiles) {
              const files = window.electronAPI.listFiles(aiDir);
              console.log("Files in ai directory:", files);
            }
          } else {
            console.log("AI directory does not exist");
          }
        } catch (dirError) {
          console.log("Could not check ai directory:", dirError);
        }
      }

      return { success: false, error: error.message };
    }
  };
  const createAiVideoAnalytics = (segments, audioFilenames = {}) => {
    const analytics = {
      destinationPath: projectUrl,
    };

    segments.forEach((segment, index) => {
      const segmentKey = `segment${index + 1}`;

      // Get saved settings for this segment, or use current settings as fallback
      const settings = segmentSettings[segment.id] || {
        characterSize: characterSize,
        weather: weather,
        environment: environtment,
        groundTexture: groundTexture,
        groundMaterial: groundMaterial,
        footwear: footwear,
        fullPrompt: fullPrompt,
        negativePrompt: negativePrompt,
        seed: seed,
        qualitySounds: qualitySounds,
        guidenceStrength: guidenceStrength,
      };

      analytics[segmentKey] = {
        segmentId: segment.id,
        segmentName: segment.name,
        startTime: segment.start,
        endTime: segment.end,
        audioFile: audioFilenames[segment.id] || null,
        characterSize: settings.characterSize,
        weather: settings.weather,
        environment: settings.environment,
        groundTexture: settings.groundTexture,
        groundMaterial: settings.groundMaterial,
        footwear: settings.footwear,
        fullPrompt: settings.fullPrompt,
        settings: {
          negativePrompt: settings.negativePrompt,
          seed: settings.seed,
          qualitySounds: settings.qualitySounds,
          guidenceStrength: settings.guidenceStrength,
        },
      };
    });

    return analytics;
  };
  // Updated handleGenerate function with better error handling
  const handleGenerate = async () => {
    if (!selectedSegment) {
      alert("Please select a segment to generate audio for");
      return;
    }

    try {
      setIsLoding(true);

      // IMPORTANT: Save current settings for this segment BEFORE generating
      const currentSegmentSettings = saveCurrentSettingsForSegment(
        selectedSegment.id
      );
      console.log(
        `Saved settings for segment ${selectedSegment.name}:`,
        currentSegmentSettings
      );

      // Create analytics with individual segment settings
      const currentAnalytics = createAiVideoAnalytics(
        segments,
        segmentAudioFiles
      );

      // Save project first to get the path
      const saveResult = await saveProject((prev) => ({
        ...prev,
        aiVideoAnalytics: currentAnalytics,
      }));

      if (!saveResult.success) {
        throw new Error(
          `Failed to save project: ${saveResult.error || "Unknown error"}`
        );
      }

      const projectPath = saveResult.path || saveResult.savedPath || projectUrl;
      const savedVideoFileName = saveResult.videoFileName;
      if (!projectPath) {
        throw new Error("No project path available after saving");
      }

      // Use the saved settings for this specific segment in the request
      const requestData = {
        destinationPath: projectPath,
        analysis_type: "audio_generation",
        videoFileName: savedVideoFileName,
        request_id: `req_${Date.now()}_${selectedSegment.id}`,
        selectedSegment: {
          id: selectedSegment.id,
          name: selectedSegment.name,
          start: selectedSegment.start,
          end: selectedSegment.end,
        },
        parameters: {
          weather: currentSegmentSettings.weather,
          environment: currentSegmentSettings.environment,
          ground_texture: currentSegmentSettings.groundTexture,
          ground_material: currentSegmentSettings.groundMaterial,
          footwear: currentSegmentSettings.footwear,
          seed: currentSegmentSettings.seed,
          quality_sounds: currentSegmentSettings.qualitySounds,
          guidance_strength: currentSegmentSettings.guidenceStrength,
          full_prompt: currentSegmentSettings.fullPrompt,
          negative_prompt: currentSegmentSettings.negativePrompt,
        },
      };

      console.log(
        `🚀 Starting Python analysis for segment: ${selectedSegment.name}`
      );

      // Call Python server
      const result = await window.electronAPI.runPythonAnalysis(requestData);

      if (result.success) {
        console.log("✅ Python analysis successful:", result.data);

        // Generate audio filename
        const timestamp = Date.now();
        const segmentIndex =
          segments.findIndex((seg) => seg.id === selectedSegment.id) + 1;
        const generatedAudioFile =
          result.data.audioFile ||
          result.data.generated_file ||
          `segment_${segmentIndex}_${timestamp}.wav`;

        // Wait for file system to sync
        console.log("⏳ Waiting for file system to sync...");
        await new Promise((resolve) => setTimeout(resolve, 3000));

        // Replace segment audio
        console.log("🔄 Replacing segment audio...");
        // const audioResult = await handleReplaceSegmentAudio(generatedAudioFile);
        const audioResult = await handleReplaceSegmentAudio("1.wav");

        if (audioResult.success) {
          // Update state with new audio filename
          setSegmentAudioFiles((prev) => ({
            ...prev,
            [selectedSegment.id]: generatedAudioFile,
          }));

          // Create updated analytics with new audio filename AND preserved settings
          const updatedAudioFiles = {
            ...segmentAudioFiles,
            [selectedSegment.id]: generatedAudioFile,
          };
          const updatedAnalytics = createAiVideoAnalytics(
            segments,
            updatedAudioFiles
          );

          // Save project with updated audio filename and individual settings
          await saveProject((prev) => ({
            ...prev,
            aiVideoAnalytics: updatedAnalytics,
          }));

          console.log(
            `✅ Audio generated for segment: ${selectedSegment.name}`
          );
          console.log(`📊 Segment settings preserved:`, currentSegmentSettings);
        }

        setIsLoding(false);
        console.log(
          `🎉 Process completed for segment: ${selectedSegment.name}!`
        );
      } else {
        setIsLoding(false);
        console.error("❌ Python analysis failed:", result.error);
        throw new Error(`Analysis failed: ${result.error}`);
      }
    } catch (error) {
      setIsLoding(false);
      console.error("❌ Error in generate process:", error);
      alert(`Error: ${error.message}`);
    }
  };

  return (
    <div className="flex lg:gap-3 xl:gap-6 justify-start items-start">
      {/* Your existing JSX code remains unchanged */}
      <div className="relative">
        <div className="lg:w-9 xl:w-16 px-2 py-4 bg-gradient-to-bl from-stone-300/25 via-white/25 to-neutral-400/25 rounded-[40px] shadow-[inset_1px_1px_3px_0px_rgba(255,255,255,0.29)] outline-[0.50px] outline-offset-[-0.50px] backdrop-blur-[10px] flex flex-col justify-start items-center gap-3">
          <ItemSetting isActive={showPopup == "ai"}>
            <img
              onClick={() => {
                setShowPopup((prev) =>
                  prev == "" || prev !== "ai" ? "ai" : ""
                );
                setPage("home");
              }}
              src="./icon/sparkles.svg"
              alt="sparkles"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5"
            />
          </ItemSetting>
          <ItemSetting isActive={showPopup == "setting"}>
            <img
              onClick={() => {
                setShowPopup((prev) =>
                  prev == "" || prev !== "setting" ? "setting" : ""
                );
                setPage("home");
              }}
              src="./icon/setting.svg"
              alt="setting"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5"
            />
          </ItemSetting>
          <ItemSetting isActive={showPopup == "download"}>
            <img
              onClick={() => {
                setPage("home");
                setShowPopup((prev) =>
                  prev === "" || prev !== "download" ? "download" : ""
                );
              }}
              src="./icon/download.svg"
              alt="download"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5"
            />
          </ItemSetting>
          <ItemSetting>
            <img
              src="./icon/user.svg"
              alt="user"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5"
            />
          </ItemSetting>
          <ItemSetting isActive={showPopup == "help"}>
            <img
              onClick={() => {
                setPage("help");
                setShowPopup((prev) =>
                  prev === "" || prev !== "help" ? "help" : ""
                );
              }}
              src="./icon/help.svg"
              alt="help"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5"
            />
          </ItemSetting>
        </div>
      </div>
      {showPopup === "ai" && (
        <PopupSetting>
          <div className="flex flex-col gap-4 text-white p-4">
            <h2 className="text-sm font-bold font-inter leading-tight tracking-tight">
              AI Video Analytics
            </h2>
            <p className="text-sm font-normal font-inter leading-tight tracking-tight">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
              enim ad minim veniam, quis nostrud exercitation ullamco laboris
              nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in
              reprehenderit in voluptate velit esse cillum dolore eu fugiat
              nulla pariatur.
            </p>
            <div className="character-size">
              <h3 className="text-sm font-semibold font-inter leading-tight tracking-tight">
                Character size
              </h3>
              <FormControl>
                <RadioGroup
                  row
                  aria-labelledby="demo-row-radio-buttons-group-label"
                  name="row-radio-buttons-group"
                  defaultValue={"big"}
                  value={characterSize}
                  onChange={(e) => setCharacterSize(e.target.value)}
                >
                  <FormControlLabel
                    value="big"
                    control={
                      <Radio
                        icon={<UncheckedIcon />}
                        checkedIcon={<CheckedIcon />}
                      />
                    }
                    label="Big"
                    sx={{
                      "& .MuiFormControlLabel-label": {
                        fontSize: "14px",
                        fontFamily: "Inter, sans-serif",
                        color: "white",
                      },
                    }}
                  />
                  <FormControlLabel
                    value="medium"
                    control={
                      <Radio
                        icon={<UncheckedIcon />}
                        checkedIcon={<CheckedIcon />}
                      />
                    }
                    label="Medium"
                    sx={{
                      "& .MuiFormControlLabel-label": {
                        fontSize: "14px",
                        fontFamily: "Inter, sans-serif",
                        color: "white",
                      },
                    }}
                  />
                  <FormControlLabel
                    value="small"
                    control={
                      <Radio
                        icon={<UncheckedIcon />}
                        checkedIcon={<CheckedIcon />}
                      />
                    }
                    label="Small"
                    sx={{
                      "& .MuiFormControlLabel-label": {
                        fontSize: "14px",
                        fontFamily: "Inter, sans-serif",
                        color: "white",
                      },
                    }}
                  />
                </RadioGroup>
              </FormControl>
            </div>
            <div className="environtment-criteria flex flex-col gap-2">
              <div className="text-sm font-semibold font-inter leading-tight tracking-tight">
                Environment criteria
              </div>
              <div className="pl-2 flex flex-col gap-2">
                <SelectEnv
                  heading="Weather"
                  defaultValue={weather}
                  data={weatherItems}
                  onchange={(e) => setWeather(e.target.value)}
                />
                <SelectEnv
                  heading="Environment"
                  defaultValue={environtment}
                  data={envItem}
                  onchange={(e) => setEnvirontment(e.target.value)}
                />
                <SelectEnv
                  heading="Ground Texture"
                  defaultValue={groundTexture}
                  data={GroundTextureItems}
                  onchange={(e) => setGroundTexture(e.target.value)}
                />
                <SelectEnv
                  heading="Ground Material"
                  defaultValue={groundMaterial}
                  data={GroundMaterialItems}
                  onchange={(e) => setGroundMaterial(e.target.value)}
                />
                <SelectEnv
                  heading="Footwear"
                  defaultValue={footwear}
                  data={FootwearItems}
                  onchange={(e) => setFootwear(e.target.value)}
                />
                {/* Full prompt */}
                <div className="flex flex-col gap-2">
                  <h4 className=" text-gray-200 text-sm font-normal font-inter leading-tight tracking-tight mb-0">
                    Full Prompt
                  </h4>
                  <Box
                    component="form"
                    sx={{
                      "& .MuiTextField-root": {
                        m: 0,
                        width: "100%",
                        border: "none",
                        backgroundColor: "#1818184D",
                        "& .MuiInputBase-input": {
                          color: "white", // This targets the actual input text
                          fontSize: "14px", // Font size for the input text
                          fontFamily: "Inter, sans-serif",
                        },
                        "& .MuiOutlinedInput-root": {
                          "& fieldset": {
                            border: "none",
                          },
                          "&:hover fieldset": {
                            border: "none",
                          },
                          "&.Mui-focused fieldset": {
                            border: "none",
                          },
                        },
                      },
                    }}
                    noValidate
                    autoComplete="off"
                  >
                    <TextField
                      id="outlined-multiline-static"
                      multiline
                      rows={4}
                      value={fullPrompt}
                      onChange={(e) => setFullPrompt(e.target.value)}
                      sx={{ color: "white" }}
                    />
                  </Box>
                </div>
                {/* Video Information */}
                <div className="flex flex-col gap-2">
                  <h4 className=" text-gray-200 text-sm font-normal font-inter leading-tight tracking-tight mb-0">
                    Video Information
                  </h4>
                  <div className="flex flex-col gap-3 justify-between mt-3 px-5">
                    <p className="text-header-2">Processing Time: 00:00:44</p>
                    <p className="text-header-2">Length of Video: 00:00:06</p>
                    <p className="text-header-2">Size of Video: 1.01 MB</p>
                  </div>
                </div>
                <div className="flex flex-col gap-4 mt-8">
                  <Button
                    onClick={() => {
                      handleGenerate();
                    }}
                    disabled={!selectedSegment || isLoading}
                  >
                    {isLoading ? (
                      <img alt="loading" src="loading.gif" width={25} />
                    ) : !selectedSegment ? (
                      "Select a Segment to Generate"
                    ) : (
                      "Generate"
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </PopupSetting>
      )}

      {/* Your existing download and setting popups remain unchanged */}
      {showPopup === "download" && (
        <PopupSetting>
          <div className="flex flex-col gap-4 text-white p-4">
            <h2 className="text-sm font-bold font-inter leading-tight tracking-tight">
              Download
            </h2>

            <div className="flex flex-col gap-6">
              <div className="flex flex-col">
                <SelectEnv
                  heading="Download (Audio + Video)"
                  defaultValue={videoType}
                  data={VideoTypeItems}
                  onchange={(e) => setVideoType(e.target.value)}
                />
                <Button customClass={"mt-4"} onClick={() => {}}>
                  Download
                </Button>
              </div>
              <div className="flex flex-col">
                <SelectEnv
                  heading="Download (Audio only)"
                  defaultValue={audioType}
                  data={AudioTypeItems}
                  onchange={(e) => setAudioType(e.target.value)}
                />
                <Button customClass={"mt-4"} onClick={() => {}}>
                  Download
                </Button>
              </div>
            </div>
          </div>
        </PopupSetting>
      )}

      {showPopup === "setting" && (
        <PopupSetting>
          <div className="flex flex-col gap-4 text-white p-4">
            <h2 className="text-sm font-bold font-inter leading-tight tracking-tight">
              Negative prompt
            </h2>
            <Box
              component="form"
              sx={{
                "& .MuiTextField-root": {
                  m: 0,
                  width: "100%",
                  border: "none",
                  backgroundColor: "#1818184D",
                  "& .MuiInputBase-input": {
                    color: "white", // This targets the actual input text
                    fontSize: "14px", // Font size for the input text
                    fontFamily: "Inter, sans-serif",
                  },
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": {
                      border: "none",
                    },
                    "&:hover fieldset": {
                      border: "none",
                    },
                    "&.Mui-focused fieldset": {
                      border: "none",
                    },
                  },
                },
              }}
              noValidate
              autoComplete="off"
            >
              <TextField
                id="outlined-multiline-static"
                multiline
                rows={6}
                value={negativePrompt}
                onChange={(e) => setNegativePrompt(e.target.value)}
                sx={{ color: "white" }}
              />
            </Box>

            <div className="flex flex-col gap-2">
              {/* Item slider */}
              <div className="flex flex-col">
                <h3>Seed: {seed}</h3>
                <Box sx={{ width: "80%" }}>
                  <Slider
                    onChange={(e) => setSeed(e.target.value)}
                    aria-label="Default"
                    valueLabelDisplay="off"
                    value={seed}
                    max={100}
                  />
                </Box>
              </div>
              {/* Item slider */}
              <div className="flex flex-col">
                <h3>Quality of Sounds: {qualitySounds}</h3>
                <Box sx={{ width: "80%" }}>
                  <Slider
                    onChange={(e) => setQualitySounds(e.target.value)}
                    aria-label="Default"
                    valueLabelDisplay="off"
                    value={qualitySounds}
                    max={100}
                  />
                </Box>
              </div>
              <div className="flex flex-col">
                <h3>Guidence Strength: {guidenceStrength}</h3>
                <Box sx={{ width: "80%" }}>
                  <Slider
                    onChange={(e) => setGuidenceStrength(e.target.value)}
                    aria-label="Default"
                    valueLabelDisplay="off"
                    value={guidenceStrength}
                    max={50}
                  />
                </Box>
              </div>
            </div>
          </div>
        </PopupSetting>
      )}
    </div>
  );
};

export default VideoSetting;
