import { useState, useCallback } from "react";

export const useDragAndDrop = (onFileDrop, hasVideo = false) => {
  const [dragging, setDragging] = useState(false);
  const [isReplacing, setIsReplacing] = useState(false);

  const handleDragEnter = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      setDragging(true);
      if (hasVideo) setIsReplacing(true);
    },
    [hasVideo]
  );

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragging(false);
    setIsReplacing(false);
  }, []);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      setDragging(false);
      setIsReplacing(false);

      const files = e.dataTransfer.files;
      if (files && files.length) {
        onFileDrop(files[0]);
      }
    },
    [onFileDrop]
  );

  return {
    dragging,
    isReplacing,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
  };
};
