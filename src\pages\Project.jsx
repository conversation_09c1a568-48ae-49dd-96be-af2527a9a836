const MyProject = () => {
  return (
    <div className="flex-1 overflow-y-auto p-10">
      <h1 className="text-2xl font-bold mb-4">My Library</h1>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <div className="bg-[#1f1f1f] rounded-lg p-3 cursor-pointer hover:bg-[#2a2a2a] transition">
          <img
            src="https://mochachaipublic.blob.core.windows.net/mochachai-generated-files/WhatsApp Video 2025-04-02 at 13-2278ce54866e.jpg"
            alt="Thumbnail"
            className="w-full h-[120px] object-cover rounded mb-2"
          />
          <h3 className="text-sm font-medium truncate">WhatsApp Video 2025-04-02 at 13.25.30_528a773e.mp4</h3>
          <p className="text-xs text-gray-400">4/8/2025, 2:05:34 PM</p>
        </div>
        <div className="bg-[#1f1f1f] rounded-lg p-3 cursor-pointer hover:bg-[#2a2a2a] transition">
          <img
            src="https://mochachaipublic.blob.core.windows.net/mochachai-generated-files/WhatsApp Video 2025-04-02 at 13-2278ce54866e.jpg"
            alt="Thumbnail"
            className="w-full h-[120px] object-cover rounded mb-2"
          />
          <h3 className="text-sm font-medium truncate">WhatsApp Video 2025-04-02 at 13.25.30_528a773e.mp4</h3>
          <p className="text-xs text-gray-400">4/8/2025, 2:05:34 PM</p>
        </div>
        <div className="bg-[#1f1f1f] rounded-lg p-3 cursor-pointer hover:bg-[#2a2a2a] transition">
          <img
            src="https://mochachaipublic.blob.core.windows.net/mochachai-generated-files/WhatsApp Video 2025-04-02 at 13-2278ce54866e.jpg"
            alt="Thumbnail"
            className="w-full h-[120px] object-cover rounded mb-2"
          />
          <h3 className="text-sm font-medium truncate">WhatsApp Video 2025-04-02 at 13.25.30_528a773e.mp4</h3>
          <p className="text-xs text-gray-400">4/8/2025, 2:05:34 PM</p>
        </div>
        <div className="bg-[#1f1f1f] rounded-lg p-3 cursor-pointer hover:bg-[#2a2a2a] transition">
          <img
            src="https://mochachaipublic.blob.core.windows.net/mochachai-generated-files/WhatsApp Video 2025-04-02 at 13-2278ce54866e.jpg"
            alt="Thumbnail"
            className="w-full h-[120px] object-cover rounded mb-2"
          />
          <h3 className="text-sm font-medium truncate">WhatsApp Video 2025-04-02 at 13.25.30_528a773e.mp4</h3>
          <p className="text-xs text-gray-400">4/8/2025, 2:05:34 PM</p>
        </div>
      </div>
    </div>
  );
};

export default MyProject;
