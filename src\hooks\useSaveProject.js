import { useEffect, useRef, useCallback, useState } from "react";
import { Buffer } from "buffer";
import path from "path";

export const useSaveProject = (videoSrc, videoFile, aiVideoAnalytics) => {
  const videoSrcRef = useRef(videoSrc);
  const videoFileRef = useRef(videoFile);
  const lastSavedHashRef = useRef(null);
  const lastSavedPathRef = useRef(null);
  const [savedPath, setSavedPath] = useState(null);
  const [videoFileName, setVideoFileName] = useState("");

  useEffect(() => {
    videoSrcRef.current = videoSrc;
  }, [videoSrc]);

  useEffect(() => {
    videoFileRef.current = videoFile;
  }, [videoFile]);

  const getFileHash = async (blob) => {
    const arrayBuffer = await blob.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest("SHA-256", arrayBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
  };

  const saveProject = async (updateFunc = null) => {
    const currentSrc = videoSrcRef.current;
    const currentFile = videoFileRef.current;

    if (!currentSrc || !currentFile) {
      alert("🚫 Tidak ada video untuk disimpan.");
      return { success: false, error: "No video to save" };
    }

    try {
      let result;
      let finalAiVideoAnalytics = aiVideoAnalytics;

      // If updateFunc is provided, use it to update aiVideoAnalytics
      if (updateFunc && typeof updateFunc === "function") {
        const updated = updateFunc({ aiVideoAnalytics });
        finalAiVideoAnalytics = updated.aiVideoAnalytics || updated;
        console.log("📊 Updated aiVideoAnalytics:", finalAiVideoAnalytics);
      }

      const response = await fetch(currentSrc);
      const blob = await response.blob();
      const buffer = Buffer.from(await blob.arrayBuffer());
      const currentHash = await getFileHash(blob);

      const isSameFile = currentHash === lastSavedHashRef.current;

      const videoPath =
        isSameFile && lastSavedPathRef.current
          ? lastSavedPathRef.current
          : currentFile.name;

      // Save the video/project
      result = await window.electronAPI.saveVideo(
        videoPath,
        buffer,
        currentHash,
        finalAiVideoAnalytics
      );

      console.log("💾 Save result:", result);

      if (result.success) {
        lastSavedHashRef.current = currentHash;
        lastSavedPathRef.current = result.path;
        setSavedPath(result.path);
        setVideoFileName(result.data.savedPath);

        // Add this to store the last saved path
        const savedPath = result.path;

        // Return the saved path along with other info
        return {
          success: true,
          path: savedPath,
          videoFileName: result.data.savedPath,
          message: `Video berhasil disimpan di: ${savedPath}`,
        };
      }
    } catch (error) {
      console.error("saveProject error:", error);
      alert(`❌ Terjadi kesalahan: ${error.message}`);
      return {
        success: false,
        error: error.message || "Unknown error occurred",
      };
    }
  };

  const clearProject = useCallback(() => {
    videoSrcRef.current = null;
    videoFileRef.current = null;
    // Jangan reset lastSavedHashRef/lastSavedPathRef
  }, []);

  useEffect(() => {
    if (!window.electronAPI?.onMenuSave) return;

    const handleSave = async () => {
      const result = await saveProject();
      if (result.success) {
        let message = result.message;
        if (result.aiCopy && result.aiCopy.success) {
          message += `\n🔧 AI folder copied for development testing.`;
        }
        alert(message);
      }
      // Error alert is already handled in saveProject
    };

    window.electronAPI.onMenuSave(handleSave);
    window.electronAPI.onClearProject(clearProject);

    return () => {
      window.electronAPI.removeAllListeners("save-project");
    };
  }, []);

  return {
    clearProject,
    saveProject,
    path,
    savedPath,
    videoFileName,
  };
};
