#!/usr/bin/env python3
"""
Sound Post AI Server - FastAPI Version (Fixed)
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from pydantic import BaseModel
import uvicorn
import json
import sys
import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Sound Post AI Server",
    description="AI Analysis Server for Electron App",
    version="1.0.0"
)

# Request/Response Models
class AnalysisRequest(BaseModel):
    analysis_type: str = "general"
    audio_file: Optional[str] = None
    duration: Optional[float] = None
    request_id: Optional[str] = None
    # This allows any extra fields the client sends
    class Config:
        extra = "allow"  # or Extra.allow in newer versions

class AnalysisResponse(BaseModel):
    success: bool
    status: str
    results: Dict[str, Any]
    analysis_id: str
    processing_time: float
    timestamp: float

class HealthResponse(BaseModel):
    status: str
    message: str
    timestamp: float
    server_time: str
    models_loaded: bool
    version: str

# Global variables
models_loaded = False

@app.on_event("startup")
async def startup_event():
    """Initialize AI models when server starts"""
    global models_loaded
    logger.info("🔄 Loading AI models...")
    
    # Simulate model loading
    time.sleep(1)
    models_loaded = True
    logger.info("✅ Heavy ML models loaded successfully!")
    
    # Send ready signal to Electron (FIXED timestamp)
    ready_signal = {
        "status": "ready",
        "message": "AI server ready for analysis",
        "model_loaded": True,
        "timestamp": datetime.now().isoformat()
    }
    
    # Print JSON signal for Electron to detect
    print(json.dumps(ready_signal), flush=True)
    logger.info("🚀 AI server ready and listening...")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    logger.info("✅ Health check requested")
    
    return HealthResponse(
        status="healthy",
        message="AI server is running and ready",
        timestamp=time.time(),
        server_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        models_loaded=models_loaded,
        version="1.0.0"
    )

@app.get("/status")
async def server_status():
    """Detailed server status"""
    return {
        "status": "operational",
        "uptime": time.time(),
        "models_loaded": models_loaded,
        "active_models": ["sound_analysis", "audio_processing"] if models_loaded else [],
        "current_time": datetime.now().isoformat()
    }

@app.post("/analyze", response_model=AnalysisResponse)
async def analyze_audio(request: AnalysisRequest):
    """Main AI analysis endpoint"""
    try:
        if not models_loaded:
            raise HTTPException(status_code=503, detail="AI models not loaded yet")
        
        logger.info(f"🔄 Processing analysis request: {request.request_id or 'unknown'}")

        print("request: ", request)
        
        # Perform your actual AI analysis here
        analysis_results = perform_ai_analysis(request)
        
        response = AnalysisResponse(
            success=True,
            status="success",
            results=analysis_results,
            analysis_id=f"analysis_{int(time.time())}",
            processing_time=0.5,
            timestamp=time.time()
        )
        
        logger.info("✅ Analysis completed successfully from Python")
        return response
        
    except Exception as e:
        logger.error(f"❌ Analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def perform_ai_analysis(request: AnalysisRequest) -> Dict[str, Any]:
    """Perform the actual AI analysis"""
    # Example analysis results
    results = {
        "audio_features": {
            "duration": request.duration or 0,
            "sample_rate": 44100,
            "channels": 2
        },
        "analysis_type": request.analysis_type,
        "confidence": 0.95,
        "detected_elements": ["speech", "music", "ambient_sound"],
        "processing_info": {
            "model_version": "1.0",
            "processing_time": 0.5
        },
        "request_metadata": {
            "request_id": request.request_id,
            "processed_at": datetime.now().isoformat()
        }
    }
    
    # Add your actual AI analysis logic here
    return results

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Sound Post AI Server",
        "status": "running",
        "docs": "/docs",
        "health": "/health",
        "time": datetime.now().isoformat()
    }

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def main():
    """Main entry point"""
    port = 8000
    host = "127.0.0.1"
    
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            logger.error("Invalid port number")
            sys.exit(1)
    
    logger.info("🐍 Starting Sound Post AI Server with FastAPI...")
    
    try:
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level="info",
            access_log=False
        )
    except KeyboardInterrupt:
        logger.info("🛑 Server shutdown requested")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()