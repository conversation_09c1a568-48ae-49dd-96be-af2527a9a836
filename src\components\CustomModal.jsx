import Dialog from "@mui/material/Dialog";

const CustomModal = ({ open, onClose, title, children }) => {
  return (
    <Dialog
      className="bg-neutral-800/60 backdrop-blur-[6px]"
      onClose={onClose}
      open={open}
      slotProps={{
        paper: {
          style: {
            backgroundColor: "transparent",
            boxShadow: "none",
          },
        },
      }}
    >
      <div className="px-8 py-4 w-[358px] bg-stone-300/40 rounded-2xl shadow-[inset_1px_1px_3px_0px_rgba(255,255,255,0.29)] backdrop-blur-md flex flex-col gap-8">
        <div className="flex justify-between items-center">
          <h3 className="self-stretch justify-start text-white text-xl font-bold font-['Inter'] leading-loose">
            {title}
          </h3>
          <img
            onClick={onClose}
            src="./icon/close.svg"
            alt="close"
            className="w-4 h-4 cursor-pointer"
          />
        </div>

        {children}
      </div>
    </Dialog>
  );
};

export default CustomModal;
