import VideoPlayerWithWaveform from "../components/VideoPlayerWithWaveform";
import { useProjectManager } from "../hooks/useProjectManager";

const Home = () => {
  const { videoSrc, videoFile, setVideoData } = useProjectManager();
  return (
    <>
      {/* Video player with restored video data */}
      <VideoPlayerWithWaveform
        initialVideoSrc={videoSrc}
        initialVideoFile={videoFile}
        onVideoChange={setVideoData}
      />
    </>
  );
};

export default Home;
