import { HashRouter as Router, Routes, Route } from "react-router-dom";
import "./App.css";
import Home from "./pages/Home";
import MainLayout from "./Layout/MainLayout";
import MyProject from "./pages/Project";
import AppProvider from "./context/AppProvider";

function App() {
  return (
    <AppProvider>
      <Router>
        <Routes>
          <Route path="/" element={<MainLayout />}>
            <Route index element={<Home />} />
            <Route path="project" element={<MyProject />} />
          </Route>
        </Routes>
      </Router>
    </AppProvider>
  );
}

export default App;
