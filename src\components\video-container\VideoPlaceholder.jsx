import React from "react";

const VideoPlaceholder = ({ onUploadClick }) => {
  return (
    <div
      className="placeholder-content lg:w-[646px] xl:w-[1078px] h-full flex flex-col justify-center items-center"
      onClick={onUploadClick}
    >
      <div className="w-10 h-10 relative overflow-hidden pointer-events-none">
        <img src="./icon/camera.svg" alt="camera" />
      </div>
      <p className="w-64 pointer-events-none text-center justify-start text-white text-base font-medium leading-snug tracking-tight select-none">
        Drop a video here to start working
      </p>
    </div>
  );
};

export default VideoPlaceholder;
