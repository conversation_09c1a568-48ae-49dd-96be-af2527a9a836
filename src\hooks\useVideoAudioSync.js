// Video Audio Synchronization Controller
// Add this to your VideoPlayerWithWaveform.js or create a separate hook

import { useEffect, useRef, useCallback } from "react";

export const useVideoAudioSync = (
  segments,
  selectedSegment,
  videoPlayerProps
) => {
  const activeAudioRef = useRef(null);
  const videoElementRef = useRef(null);
  const syncIntervalRef = useRef(null);
  const originalVideoVolumeRef = useRef(1);

  // Find segments with replaced audio
  const segmentsWithAudio = segments.filter(
    (segment) => segment.hasReplacedAudio && segment.audioElement
  );

  // Get video element reference
  useEffect(() => {
    const videoElement = document.querySelector("video");
    videoElementRef.current = videoElement;

    if (videoElement) {
      // Store original video volume
      originalVideoVolumeRef.current = videoElement.volume;
      console.log(
        "🎥 Video element found, original volume:",
        originalVideoVolumeRef.current
      );
    }
  }, [videoPlayerProps.videoSrc]);

  // Main audio synchronization function
  const syncAudioWithVideo = useCallback(() => {
    const videoElement = videoElementRef.current;
    if (!videoElement || segmentsWithAudio.length === 0) return;

    const currentTime = videoElement.currentTime;
    const isVideoPlaying = !videoElement.paused;

    // Find which segment the current time falls into
    const currentSegment = segmentsWithAudio.find(
      (segment) => currentTime >= segment.start && currentTime <= segment.end
    );

    // Handle audio switching
    if (currentSegment && currentSegment.audioElement) {
      // We're in a segment with replaced audio

      if (activeAudioRef.current !== currentSegment.audioElement) {
        // Switch to new audio
        console.log(
          `🔄 Switching to audio for segment: ${currentSegment.name}`
        );

        // Stop previous audio
        if (activeAudioRef.current) {
          activeAudioRef.current.pause();
          activeAudioRef.current.currentTime = 0;
        }

        // Start new audio
        activeAudioRef.current = currentSegment.audioElement;

        // Mute original video audio
        videoElement.volume = 0;

        // Sync audio timing with segment
        const segmentProgress = currentTime - currentSegment.start;
        const segmentDuration = currentSegment.end - currentSegment.start;
        const audioProgress =
          (segmentProgress / segmentDuration) * activeAudioRef.current.duration;

        activeAudioRef.current.currentTime = Math.max(0, audioProgress);

        if (isVideoPlaying) {
          activeAudioRef.current
            .play()
            .catch((err) => console.warn("Audio play failed:", err));
        } else {
          activeAudioRef.current.pause();
        }
      } else if (activeAudioRef.current) {
        // Same audio, just sync playback state
        if (isVideoPlaying && activeAudioRef.current.paused) {
          activeAudioRef.current
            .play()
            .catch((err) => console.warn("Audio play failed:", err));
        } else if (!isVideoPlaying && !activeAudioRef.current.paused) {
          activeAudioRef.current.pause();
        }
      }
    } else {
      // We're not in a segment with replaced audio
      if (activeAudioRef.current) {
        console.log(
          "🔇 Stopping replaced audio, restoring original video audio"
        );

        // Stop replaced audio
        activeAudioRef.current.pause();
        activeAudioRef.current.currentTime = 0;
        activeAudioRef.current = null;

        // Restore original video audio
        videoElement.volume = originalVideoVolumeRef.current;
      }
    }
  }, [segmentsWithAudio]);

  // Set up continuous synchronization
  useEffect(() => {
    if (segmentsWithAudio.length === 0) return;

    console.log(
      "🎵 Setting up audio sync for",
      segmentsWithAudio.length,
      "segments"
    );

    // Start sync interval
    syncIntervalRef.current = setInterval(syncAudioWithVideo, 100); // Check every 100ms

    // Also sync on video events
    const videoElement = videoElementRef.current;
    if (videoElement) {
      const handleVideoEvent = () => {
        setTimeout(syncAudioWithVideo, 50); // Small delay to ensure video state is updated
      };

      videoElement.addEventListener("play", handleVideoEvent);
      videoElement.addEventListener("pause", handleVideoEvent);
      videoElement.addEventListener("seeked", handleVideoEvent);
      videoElement.addEventListener("timeupdate", syncAudioWithVideo);

      return () => {
        // Cleanup
        if (syncIntervalRef.current) {
          clearInterval(syncIntervalRef.current);
        }

        videoElement.removeEventListener("play", handleVideoEvent);
        videoElement.removeEventListener("pause", handleVideoEvent);
        videoElement.removeEventListener("seeked", handleVideoEvent);
        videoElement.removeEventListener("timeupdate", syncAudioWithVideo);

        // Stop any active audio
        if (activeAudioRef.current) {
          activeAudioRef.current.pause();
          activeAudioRef.current = null;
        }

        // Restore video volume
        videoElement.volume = originalVideoVolumeRef.current;
      };
    }

    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
      }
    };
  }, [segmentsWithAudio, syncAudioWithVideo]);

  // Handle video volume changes
  useEffect(() => {
    const videoElement = videoElementRef.current;
    if (!videoElement) return;

    const handleVolumeChange = () => {
      if (!activeAudioRef.current) {
        // Only update original volume reference when not playing replaced audio
        originalVideoVolumeRef.current = videoElement.volume;
      }

      // If we have active replaced audio, sync its volume
      if (activeAudioRef.current) {
        activeAudioRef.current.volume = videoElement.volume;
        videoElement.volume = 0; // Keep video muted
      }
    };

    videoElement.addEventListener("volumechange", handleVolumeChange);

    return () => {
      videoElement.removeEventListener("volumechange", handleVolumeChange);
    };
  }, []);

  // Manual play function for testing (you can remove this)
  const playSegmentAudio = useCallback((segment) => {
    if (segment && segment.audioElement) {
      console.log("🎵 Manually playing audio for segment:", segment.name);
      segment.audioElement.currentTime = 0;
      segment.audioElement
        .play()
        .catch((err) => console.warn("Manual audio play failed:", err));
    }
  }, []);

  return {
    activeAudio: activeAudioRef.current,
    playSegmentAudio, // For manual testing
    segmentsWithAudio: segmentsWithAudio.length,
  };
};
