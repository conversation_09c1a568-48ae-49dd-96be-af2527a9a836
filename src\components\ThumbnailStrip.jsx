/* eslint-disable no-unused-vars */
// ThumbnailStrip.jsx
import React, { useState, useEffect, useRef, useCallback } from "react";

const ThumbnailStrip = ({
  videoSrc,
  duration,
  currentTime = 0,
  zoomLevel,
  isSliderDragging = false,
  onGeneratingChange,
  onTimeChange,
  onSegmentSelect,
  onSegmentsChange,
}) => {
  const [thumbnails, setThumbnails] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [hoveredThumbnail, setHoveredThumbnail] = useState(null);
  const [segments, setSegments] = useState([]);
  const [selectedSegment, setSelectedSegment] = useState(null);
  const [dragMode, setDragMode] = useState(null);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartTime, setDragStartTime] = useState(0);

  const prevZoomLevelRef = useRef(zoomLevel);
  const prevVideoSrcRef = useRef(videoSrc);
  const timelineRef = useRef(null);
  const containerRef = useRef(null);

  useEffect(() => {
    onGeneratingChange?.(isGenerating);
  }, [isGenerating, onGeneratingChange]);

  useEffect(() => {
    onSegmentsChange?.(segments);
  }, [segments, onSegmentsChange]);

  // Generate thumbnails
  useEffect(() => {
    if (!videoSrc || !duration || isGenerating || isSliderDragging) return;

    const generateThumbnails = async () => {
      const zoomChanged = prevZoomLevelRef.current !== zoomLevel;
      const videoChanged = prevVideoSrcRef.current !== videoSrc;

      if (!zoomChanged && !videoChanged && thumbnails.length > 0) {
        return;
      }

      setIsGenerating(true);
      prevZoomLevelRef.current = zoomLevel;
      prevVideoSrcRef.current = videoSrc;

      const video = document.createElement("video");
      video.src = videoSrc;
      video.crossOrigin = "anonymous";

      try {
        await new Promise((resolve, reject) => {
          const handleLoadedMetadata = () => {
            video.removeEventListener("error", handleError);
            resolve();
          };

          const handleError = () => {
            video.removeEventListener("loadedmetadata", handleLoadedMetadata);
            reject(new Error("Video metadata load failed"));
          };

          video.addEventListener("loadedmetadata", handleLoadedMetadata, {
            once: true,
          });
          video.addEventListener("error", handleError, { once: true });
          video.load();
        });

        const canvas = document.createElement("canvas");
        canvas.width = 160;
        canvas.height = 90; // 16:9 aspect ratio
        const ctx = canvas.getContext("2d");

        const thumbnailCount = Math.max(10, Math.floor(zoomLevel * 2)); // Reasonable number of thumbnails
        const newThumbnails = [];

        for (let i = 0; i < thumbnailCount; i++) {
          const time = (i / (thumbnailCount - 1)) * duration;
          video.currentTime = time;

          await new Promise((resolve, reject) => {
            const handleSeeked = () => {
              video.removeEventListener("error", handleSeekError);
              resolve();
            };

            const handleSeekError = () => {
              video.removeEventListener("seeked", handleSeeked);
              reject(new Error("Video seek failed"));
            };

            video.addEventListener("seeked", handleSeeked, { once: true });
            video.addEventListener("error", handleSeekError, { once: true });
          });

          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
          newThumbnails.push({
            time,
            url: canvas.toDataURL("image/jpeg", 0.7),
          });
        }

        setThumbnails(newThumbnails);
      } catch (error) {
        console.error("Error generating thumbnails:", error);
      } finally {
        video.src = "";
        setIsGenerating(false);
      }
    };

    const timeoutId = setTimeout(generateThumbnails, 100);
    return () => clearTimeout(timeoutId);
  }, [
    zoomLevel,
    videoSrc,
    duration,
    isGenerating,
    isSliderDragging,
    thumbnails.length,
  ]);

  // Convert pixel position to time
  const pixelToTime = useCallback(
    (x) => {
      if (!containerRef.current) return 0;
      const rect = containerRef.current.getBoundingClientRect();
      const percentage = Math.max(0, Math.min(1, x / rect.width));
      return percentage * duration;
    },
    [duration]
  );

  // Convert time to pixel position
  const timeToPixel = useCallback(
    (time) => {
      if (!containerRef.current) return 0;
      const rect = containerRef.current.getBoundingClientRect();
      return (time / duration) * rect.width;
    },
    [duration]
  );

  // Handle mouse down on timeline - SHIFT FUNCTIONALITY REMOVED
  const handleMouseDown = useCallback(
    (e) => {
      if (!containerRef.current) return;

      const rect = containerRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const clickTime = pixelToTime(x);

      setDragStartX(x);
      setDragStartTime(clickTime);

      // Check if clicking on segment handles
      const clickedSegment = segments.find((segment) => {
        const startX = timeToPixel(segment.start);
        const endX = timeToPixel(segment.end);

        // Check start handle (left 8px)
        if (Math.abs(x - startX) <= 8) {
          setDragMode("trim-start");
          setSelectedSegment(segment);
          return true;
        }

        // Check end handle (right 8px)
        if (Math.abs(x - endX) <= 8) {
          setDragMode("trim-end");
          setSelectedSegment(segment);
          return true;
        }

        // Check if clicking inside segment
        if (x >= startX && x <= endX) {
          setDragMode("segment");
          setSelectedSegment(segment);
          onSegmentSelect?.(segment);
          return true;
        }

        return false;
      });

      if (!clickedSegment) {
        // Only handle seeking - no segment creation
        setDragMode("seek");
        onTimeChange?.(clickTime);
        setSelectedSegment(null);
        onSegmentSelect?.(null);
      }
    },
    [segments, pixelToTime, timeToPixel, onTimeChange, onSegmentSelect]
  );

  // Handle mouse move
  const handleMouseMove = useCallback(
    (e) => {
      if (!containerRef.current) return;

      const rect = containerRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const currentDragTime = pixelToTime(x);

      if (dragMode) {
        switch (dragMode) {
          case "seek":
            onTimeChange?.(Math.max(0, Math.min(duration, currentDragTime)));
            break;

          case "trim-start":
            if (selectedSegment) {
              const newStart = Math.max(
                0,
                Math.min(selectedSegment.end - 0.1, currentDragTime)
              );
              const updatedSegments = segments.map((seg) =>
                seg === selectedSegment ? { ...seg, start: newStart } : seg
              );
              setSegments(updatedSegments);
              setSelectedSegment({ ...selectedSegment, start: newStart });
              onSegmentSelect?.({ ...selectedSegment, start: newStart });
            }
            break;

          case "trim-end":
            if (selectedSegment) {
              const newEnd = Math.max(
                selectedSegment.start + 0.1,
                Math.min(duration, currentDragTime)
              );
              const updatedSegments = segments.map((seg) =>
                seg === selectedSegment ? { ...seg, end: newEnd } : seg
              );
              setSegments(updatedSegments);
              setSelectedSegment({ ...selectedSegment, end: newEnd });
              onSegmentSelect?.({ ...selectedSegment, end: newEnd });
            }
            break;

          case "segment":
            if (selectedSegment) {
              const deltaTime = currentDragTime - dragStartTime;
              const segmentDuration =
                selectedSegment.end - selectedSegment.start;
              const newStart = Math.max(
                0,
                Math.min(
                  duration - segmentDuration,
                  selectedSegment.start + deltaTime
                )
              );
              const newEnd = newStart + segmentDuration;

              const updatedSegments = segments.map((seg) =>
                seg === selectedSegment
                  ? { ...seg, start: newStart, end: newEnd }
                  : seg
              );
              setSegments(updatedSegments);
              const updatedSegment = {
                ...selectedSegment,
                start: newStart,
                end: newEnd,
              };
              setSelectedSegment(updatedSegment);
              onSegmentSelect?.(updatedSegment);
            }
            break;
        }
      } else {
        // Show hover preview
        const hoverTime = currentDragTime;
        const hoverThumbnail = thumbnails.reduce((prev, curr) =>
          Math.abs(curr.time - hoverTime) < Math.abs(prev.time - hoverTime)
            ? curr
            : prev
        );
        setHoveredThumbnail(hoverThumbnail);
      }
    },
    [
      dragMode,
      selectedSegment,
      segments,
      pixelToTime,
      duration,
      dragStartTime,
      onTimeChange,
      onSegmentSelect,
      thumbnails,
    ]
  );

  // Handle mouse up - simplified without segment creation
  const handleMouseUp = useCallback((e) => {
    setDragMode(null);
    setDragStartX(0);
    setDragStartTime(0);
  }, []);

  // Add event listeners
  useEffect(() => {
    const handleGlobalMouseMove = (e) => handleMouseMove(e);
    const handleGlobalMouseUp = (e) => handleMouseUp(e);

    if (dragMode) {
      document.addEventListener("mousemove", handleGlobalMouseMove);
      document.addEventListener("mouseup", handleGlobalMouseUp);
    }

    return () => {
      document.removeEventListener("mousemove", handleGlobalMouseMove);
      document.removeEventListener("mouseup", handleGlobalMouseUp);
    };
  }, [dragMode, handleMouseMove, handleMouseUp]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Delete" && selectedSegment) {
        setSegments((prev) => prev.filter((seg) => seg !== selectedSegment));
        setSelectedSegment(null);
        onSegmentSelect?.(null);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [selectedSegment, onSegmentSelect]);

  // Format time
  const formatTime = (timeInSeconds) => {
    if (!timeInSeconds && timeInSeconds !== 0) return "00:00";
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  };

  const getCurrentThumbnail = () => {
    if (!thumbnails.length) return null;
    return thumbnails.reduce((prev, curr) =>
      Math.abs(curr.time - currentTime) < Math.abs(prev.time - currentTime)
        ? curr
        : prev
    );
  };

  const currentThumbnail = getCurrentThumbnail();

  return (
    <div className="relative" ref={timelineRef}>
      {/* Main timeline container */}
      <div
        ref={containerRef}
        className="thumbnails-container relative h-20 bg-neutral-800/50 rounded cursor-pointer"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseLeave={() => setHoveredThumbnail(null)}
      >
        {/* Time markers based on zoom level */}
        {!isGenerating &&
          (() => {
            const markers = [];

            // Determine interval based on zoom level
            let interval;
            if (zoomLevel >= 10) {
              interval = 0.5; // 500ms when heavily zoomed
            } else if (zoomLevel >= 5) {
              interval = 1; // 1 second
            } else if (zoomLevel >= 3) {
              interval = 2; // 2 seconds
            } else if (zoomLevel >= 2) {
              interval = 5; // 5 seconds
            } else {
              interval = 10; // 10 seconds when zoomed out
            }

            for (let time = 0; time <= Math.ceil(duration); time += interval) {
              if (time > duration) break;
              const position = (time / duration) * 100;
              markers.push(
                <div
                  key={`marker-${time}`}
                  className="absolute pointer-events-none z-10"
                  style={{
                    left: `${position}%`,
                    top: "-20px",
                    transform: "translateX(-50%)",
                  }}
                >
                  <div className="bg-neutral-800/90 text-white text-xs px-1 py-0.5 font-mono rounded whitespace-nowrap">
                    {interval < 1
                      ? `${(time * 1000).toFixed(0)}ms`
                      : formatTime(time)}
                  </div>
                </div>
              );
            }
            return markers;
          })()}

        {/* Thumbnails */}
        {!isGenerating &&
          thumbnails.map((thumbnail, index) => {
            // Calculate thumbnail width based on time duration each thumbnail should represent
            const timePerThumbnail = duration / thumbnails.length;
            const thumbnailStartTime = index * timePerThumbnail;
            const thumbnailEndTime = (index + 1) * timePerThumbnail;

            // Position based on actual time positions
            const startPosition = (thumbnailStartTime / duration) * 100;
            const endPosition = (thumbnailEndTime / duration) * 100;
            const width = endPosition - startPosition;

            return (
              <div
                key={index}
                className="thumbnail-item absolute overflow-hidden pointer-events-none"
                style={{
                  left: `${startPosition}%`,
                  width: `${width}%`,
                  top: "20px",
                  height: "calc(100% - 20px)",
                }}
              >
                <img
                  src={thumbnail.url}
                  alt={`Thumbnail at ${thumbnail.time}s`}
                  className="w-full h-full border-r border-neutral-600/30"
                  style={{
                    opacity: thumbnail.time > currentTime ? 0.3 : 1,
                    filter:
                      thumbnail.time > currentTime ? "brightness(0.5)" : "none",
                    objectFit: "cover",
                    objectPosition: "center",
                    aspectRatio: "16/9",
                  }}
                />
              </div>
            );
          })}

        {/* Segments */}
        {segments.map((segment) => (
          <div
            key={segment.id}
            className={`absolute top-0 bottom-0 bg-blue-500/60 border-2 rounded-sm ${
              selectedSegment === segment
                ? "border-blue-300"
                : "border-blue-500"
            }`}
            style={{
              left: `${(segment.start / duration) * 100}%`,
              width: `${((segment.end - segment.start) / duration) * 100}%`,
            }}
          >
            {/* Segment label */}
            <div className="absolute top-1 left-1 text-xs text-white bg-black/50 px-1 rounded">
              {segment.name}
            </div>

            {/* Trim handles */}
            <div className="absolute left-0 top-0 bottom-0 w-2 bg-blue-300 cursor-ew-resize opacity-0 hover:opacity-100 transition-opacity" />
            <div className="absolute right-0 top-0 bottom-0 w-2 bg-blue-300 cursor-ew-resize opacity-0 hover:opacity-100 transition-opacity" />
          </div>
        ))}

        {/* Current time indicator */}
        <div
          className="absolute top-0 bottom-0 w-0.5 bg-white z-20 pointer-events-none"
          style={{
            left: `${(currentTime / duration) * 100}%`,
            transform: "translateX(-50%)",
          }}
        />
      </div>

      {/* Current thumbnail overlay */}
      {currentThumbnail && !isGenerating && (
        <div
          className="absolute -top-28 transform -translate-x-1/2 z-30"
          style={{ left: `${(currentTime / duration) * 100}%` }}
        >
          <div className="bg-black p-1 rounded shadow-lg">
            <img
              src={currentThumbnail.url}
              alt={`Current position: ${formatTime(currentTime)}`}
              className="w-40 h-24 object-cover rounded"
            />
            <div className="absolute bottom-1 left-1 right-1 text-center text-xs text-white bg-black/50 py-0.5 rounded">
              {formatTime(currentTime)}
            </div>
          </div>
          <div className="w-0 h-0 border-l-8 border-r-8 border-t-8 border-transparent border-t-black mx-auto"></div>
        </div>
      )}

      {/* Hover thumbnail overlay */}
      {hoveredThumbnail &&
        hoveredThumbnail !== currentThumbnail &&
        !isGenerating &&
        !dragMode && (
          <div
            className="absolute -top-28 transform -translate-x-1/2 z-30"
            style={{ left: `${(hoveredThumbnail.time / duration) * 100}%` }}
          >
            <div className="bg-black p-1 rounded shadow-lg">
              <img
                src={hoveredThumbnail.url}
                alt={`Hover position: ${formatTime(hoveredThumbnail.time)}`}
                className="w-40 h-24 object-cover rounded"
              />
              <div className="absolute bottom-1 left-1 right-1 text-center text-xs text-white bg-black/50 py-0.5 rounded">
                {formatTime(hoveredThumbnail.time)}
              </div>
            </div>
            <div className="w-0 h-0 border-l-8 border-r-8 border-t-8 border-transparent border-t-black mx-auto"></div>
          </div>
        )}
    </div>
  );
};

export default ThumbnailStrip;
