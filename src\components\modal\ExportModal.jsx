import {
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
} from "@mui/material";
import { useProjectManager } from "../../hooks/useProjectManager";
import { CheckedIcon, UncheckedIcon } from "../icon/CutomIcon";
import CustomModal from "../CustomModal";
import Button from "../Button";

const ExportModal = () => {
  const { modalExport, setModalExport } = useProjectManager();
  return (
    <CustomModal
      open={modalExport}
      onClose={() => setModalExport(false)}
      title="Export media as.."
    >
      <FormControl>
        <RadioGroup
          className="gap-4"
          col
          name="col-radio-buttons-group"
          defaultValue={"aaf"}
        >
          <FormControlLabel
            value="aaf"
            control={
              <Radio icon={<UncheckedIcon />} checkedIcon={<CheckedIcon />} />
            }
            label="AAF"
            sx={{
              "& .MuiFormControlLabel-label": {
                fontSize: "14px",
                fontFamily: "Inter, sans-serif",
                color: "white",
              },
            }}
          />
          <FormControlLabel
            value="wav"
            control={
              <Radio icon={<UncheckedIcon />} checkedIcon={<CheckedIcon />} />
            }
            label="WAV"
            sx={{
              "& .MuiFormControlLabel-label": {
                fontSize: "14px",
                fontFamily: "Inter, sans-serif",
                color: "white",
              },
            }}
          />
        </RadioGroup>
        <div className="flex gap-4 mt-8 items-center w-full">
          <Button
            customClass={"flex-grow shrink-0 text-white font-inter"}
            variant="danger"
            onClick={() => setModalExport(false)}
          >
            Cancel
          </Button>
          <Button customClass={"flex-grow shrink-0 text-white font-inter"}>
            Import
          </Button>
        </div>
      </FormControl>
    </CustomModal>
  );
};

export default ExportModal;
