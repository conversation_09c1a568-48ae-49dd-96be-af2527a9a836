"use client";

// UI Components
import { SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "./components/sidebar/app-sidebar";
import { Editor } from "./components/core/editor";
import { SidebarProvider as UISidebarProvider } from "@/components/ui/sidebar";
import { SidebarProvider as EditorSidebarProvider } from "./contexts/sidebar-context";

// Context Providers
import { EditorProvider } from "./contexts/editor-context";

// Custom Hooks
import { useOverlays } from "./hooks/use-overlays";
import { useVideoPlayer } from "./hooks/use-video-player";
import { useTimelineClick } from "./hooks/use-timeline-click";
import { useAspectRatio } from "./hooks/use-aspect-ratio";
import { useCompositionDuration } from "./hooks/use-composition-duration";
import { useHistory } from "./hooks/use-history";

// Types
import { Overlay } from "./types";
import { useRendering } from "./hooks/use-rendering";
import {
  AUTO_SAVE_INTERVAL,
  DEFAULT_OVERLAYS,
  FPS,
  RENDER_TYPE,
} from "./constants";
import { TimelineProvider } from "./contexts/timeline-context";

// Autosave Components
import { AutosaveStatus } from "./components/autosave/autosave-status";
import { useState, useEffect } from "react";
import { useAutosave } from "./hooks/use-autosave";
import { LocalMediaProvider } from "./contexts/local-media-context";
import { KeyframeProvider } from "./contexts/keyframe-context";
import { AssetLoadingProvider } from "./contexts/asset-loading-context";

// File processing hook (you'll need to create this or import from previous implementation)
const useFileProcessing = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);

  const createVideoOverlay = async (file: File): Promise<Overlay> => {
    return new Promise((resolve) => {
      const video = document.createElement("video");
      const url = URL.createObjectURL(file);

      video.onloadedmetadata = () => {
        const duration = video.duration;
        const durationInFrames = Math.floor(duration * 30); // Assuming 30 FPS

        const overlay: Overlay = {
          id: `video-${Date.now()}-${Math.random()}`,
          type: "video",
          src: url,
          name: file.name,
          startFrame: 0,
          endFrame: durationInFrames,
          row: 0,
          styles: {
            width: video.videoWidth,
            height: video.videoHeight,
            x: 0,
            y: 0,
            scale: 1,
            rotation: 0,
            opacity: 1,
          },
          volume: 1,
          originalFile: file,
        };

        resolve(overlay);
      };

      video.onerror = () => {
        // Fallback for unsupported video formats
        const overlay: Overlay = {
          id: `video-${Date.now()}-${Math.random()}`,
          type: "video",
          src: url,
          name: file.name,
          startFrame: 0,
          endFrame: 900, // Default 30 seconds at 30 FPS
          row: 0,
          styles: {
            width: 1920,
            height: 1080,
            x: 0,
            y: 0,
            scale: 1,
            rotation: 0,
            opacity: 1,
          },
          volume: 1,
          originalFile: file,
        };

        resolve(overlay);
      };

      video.src = url;
    });
  };

  const createAudioOverlay = async (file: File): Promise<Overlay> => {
    return new Promise((resolve) => {
      const audio = document.createElement("audio");
      const url = URL.createObjectURL(file);

      audio.onloadedmetadata = () => {
        const duration = audio.duration;
        const durationInFrames = Math.floor(duration * 30);

        const overlay: Overlay = {
          id: `audio-${Date.now()}-${Math.random()}`,
          type: "audio",
          src: url,
          name: file.name,
          startFrame: 0,
          endFrame: durationInFrames,
          row: 1,
          styles: {
            width: 0,
            height: 0,
            x: 0,
            y: 0,
            scale: 1,
            rotation: 0,
            opacity: 1,
          },
          volume: 1,
          originalFile: file,
        };

        resolve(overlay);
      };

      audio.onerror = () => {
        const overlay: Overlay = {
          id: `audio-${Date.now()}-${Math.random()}`,
          type: "audio",
          src: url,
          name: file.name,
          startFrame: 0,
          endFrame: 900,
          row: 1,
          styles: {
            width: 0,
            height: 0,
            x: 0,
            y: 0,
            scale: 1,
            rotation: 0,
            opacity: 1,
          },
          volume: 1,
          originalFile: file,
        };

        resolve(overlay);
      };

      audio.src = url;
    });
  };

  const createImageOverlay = async (file: File): Promise<Overlay> => {
    return new Promise((resolve) => {
      const img = document.createElement("img");
      const url = URL.createObjectURL(file);

      img.onload = () => {
        const overlay: Overlay = {
          id: `image-${Date.now()}-${Math.random()}`,
          type: "image",
          src: url,
          name: file.name,
          startFrame: 0,
          endFrame: 150, // Default 5 seconds at 30 FPS
          row: 2,
          styles: {
            width: img.naturalWidth,
            height: img.naturalHeight,
            x: 0,
            y: 0,
            scale: 1,
            rotation: 0,
            opacity: 1,
          },
          volume: 0,
          originalFile: file,
        };

        resolve(overlay);
      };

      img.onerror = () => {
        const overlay: Overlay = {
          id: `image-${Date.now()}-${Math.random()}`,
          type: "image",
          src: url,
          name: file.name,
          startFrame: 0,
          endFrame: 150,
          row: 2,
          styles: {
            width: 1920,
            height: 1080,
            x: 0,
            y: 0,
            scale: 1,
            rotation: 0,
            opacity: 1,
          },
          volume: 0,
          originalFile: file,
        };

        resolve(overlay);
      };

      img.src = url;
    });
  };

  const processFiles = async (files: File[]): Promise<Overlay[]> => {
    setIsProcessing(true);
    setProcessingProgress(0);

    const overlays: Overlay[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const extension = "." + file.name.split(".").pop()?.toLowerCase();

      try {
        let overlay: Overlay;

        if (
          [".mp4", ".mov", ".avi", ".mkv", ".webm", ".m4v"].includes(extension)
        ) {
          overlay = await createVideoOverlay(file);
        } else if (
          [".mp3", ".wav", ".aac", ".m4a", ".ogg", ".flac"].includes(extension)
        ) {
          overlay = await createAudioOverlay(file);
        } else if (
          [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"].includes(
            extension
          )
        ) {
          overlay = await createImageOverlay(file);
        } else {
          continue;
        }

        overlays.push(overlay);
        setProcessingProgress(((i + 1) / files.length) * 100);
      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error);
      }
    }

    setIsProcessing(false);
    setProcessingProgress(0);

    return overlays;
  };

  return {
    processFiles,
    isProcessing,
    processingProgress,
  };
};

export default function ReactVideoEditor({ projectId }: { projectId: string }) {
  // Autosave state
  const [showRecoveryDialog, setShowRecoveryDialog] = useState(false);
  const [autosaveTimestamp, setAutosaveTimestamp] = useState<number | null>(
    null
  );
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaveTime, setLastSaveTime] = useState<number | null>(null);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);

  // File processing hook
  const { processFiles, isProcessing, processingProgress } =
    useFileProcessing();

  // Overlay management hooks - Initialize with empty array
  const {
    overlays,
    setOverlays,
    selectedOverlayId,
    setSelectedOverlayId,
    changeOverlay,
    addOverlay,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,
    deleteOverlaysByRow,
    updateOverlayStyles,
    resetOverlays,
  } = useOverlays([]); // Changed from DEFAULT_OVERLAYS to []

  // Video player controls and state
  const { isPlaying, currentFrame, playerRef, togglePlayPause, formatTime } =
    useVideoPlayer();

  // Composition duration calculations
  const { durationInFrames, durationInSeconds } =
    useCompositionDuration(overlays);

  // Aspect ratio and player dimension management
  const {
    aspectRatio,
    setAspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
  } = useAspectRatio();

  // Handle file drop/upload
  const handleFilesAdded = async (files: File[]) => {
    const newOverlays = await processFiles(files);

    // Add new overlays to the timeline
    newOverlays.forEach((overlay) => {
      // Adjust positioning to avoid conflicts
      const existingRowOverlays = overlays.filter((o) => o.row === overlay.row);
      if (existingRowOverlays.length > 0) {
        const lastOverlay = existingRowOverlays[existingRowOverlays.length - 1];
        overlay.startFrame = lastOverlay.endFrame;
        overlay.endFrame =
          overlay.startFrame + (overlay.endFrame - overlay.startFrame);
      }

      addOverlay(overlay);
    });
  };

  // Event handlers
  const handleOverlayChange = (updatedOverlay: Overlay) => {
    changeOverlay(updatedOverlay.id, () => updatedOverlay);
  };

  const { width: compositionWidth, height: compositionHeight } =
    getAspectRatioDimensions();

  const handleTimelineClick = useTimelineClick(playerRef, durationInFrames);

  const inputProps = {
    overlays,
    durationInFrames,
    fps: FPS,
    width: compositionWidth,
    height: compositionHeight,
    src: "",
  };

  const { renderMedia, state } = useRendering(
    "TestComponent",
    inputProps,
    RENDER_TYPE
  );

  // Replace history management code with hook
  const { undo, redo, canUndo, canRedo } = useHistory(overlays, setOverlays);

  // Create the editor state object to be saved
  const editorState = {
    overlays,
    aspectRatio,
    playerDimensions,
  };

  // Implement load state
  const { saveState, loadState } = useAutosave(projectId, editorState, {
    interval: AUTO_SAVE_INTERVAL,
    onSave: () => {
      setIsSaving(false);
      setLastSaveTime(Date.now());
    },
    onLoad: (loadedState) => {
      console.log("loadedState", loadedState);
      if (loadedState) {
        // Apply loaded state to editor - ensure empty array fallback
        setOverlays(loadedState.overlays || []); // This ensures empty array if no overlays saved
        if (loadedState.aspectRatio) setAspectRatio(loadedState.aspectRatio);
        if (loadedState.playerDimensions)
          updatePlayerDimensions(
            loadedState.playerDimensions.width,
            loadedState.playerDimensions.height
          );
      }
    },
    onAutosaveDetected: (timestamp) => {
      // Only show recovery dialog on initial load, not during an active session
      if (!initialLoadComplete) {
        setAutosaveTimestamp(timestamp);
        setShowRecoveryDialog(true);
      }
    },
  });

  // Mark initial load as complete after component mounts
  useEffect(() => {
    setInitialLoadComplete(true);
  }, []);

  console.log("DEBUG: overlays", overlays);

  // Manual save function for use in keyboard shortcuts or save button
  const handleManualSave = async () => {
    setIsSaving(true);
    await saveState();
  };

  // Combine all editor context values
  const editorContextValue = {
    // Overlay management
    overlays,
    setOverlays,
    selectedOverlayId,
    setSelectedOverlayId,
    changeOverlay,
    handleOverlayChange,
    addOverlay,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,
    resetOverlays,

    // Player controls
    isPlaying,
    currentFrame,
    playerRef,
    togglePlayPause,
    formatTime,
    handleTimelineClick,
    playbackRate,
    setPlaybackRate,

    // Dimensions and duration
    aspectRatio,
    setAspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
    durationInFrames,
    durationInSeconds,

    // Add renderType to the context
    renderType: RENDER_TYPE,
    renderMedia,
    state,

    deleteOverlaysByRow,

    // History management
    undo,
    redo,
    canUndo,
    canRedo,

    // New style management
    updateOverlayStyles,

    // Autosave
    saveProject: handleManualSave,

    // File processing
    handleFilesAdded,
    isProcessing,
    processingProgress,
  };

  return (
    <UISidebarProvider>
      <EditorSidebarProvider>
        <KeyframeProvider>
          <TimelineProvider>
            <EditorProvider value={editorContextValue}>
              <LocalMediaProvider>
                <AssetLoadingProvider>
                  {/* <AppSidebar /> */}
                  <SidebarInset>
                    <Editor />
                  </SidebarInset>

                  {/* Autosave Status Indicator */}
                  <AutosaveStatus
                    isSaving={isSaving}
                    lastSaveTime={lastSaveTime}
                  />

                  {/* File Processing Indicator */}
                  {isProcessing && (
                    <div className="fixed bottom-4 right-4 bg-background border rounded-lg p-4 shadow-lg z-50">
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent" />
                        <span className="text-sm">
                          Processing files... {Math.round(processingProgress)}%
                        </span>
                      </div>
                    </div>
                  )}
                </AssetLoadingProvider>
              </LocalMediaProvider>
            </EditorProvider>
          </TimelineProvider>
        </KeyframeProvider>
      </EditorSidebarProvider>
    </UISidebarProvider>
  );
}
